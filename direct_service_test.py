#!/usr/bin/env python3
"""
直接服务启动测试 - 绕过GUI界面
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

def test_direct_service():
    """直接测试服务启动"""
    print("🔧 直接测试服务启动...")
    
    try:
        # 加载环境变量
        load_dotenv()
        
        # 导入主模块
        print("📦 导入main模块...")
        import main
        
        # 检查配置
        print("\n1️⃣ 检查配置...")
        cookies_str = main.config.security.cookies_str
        if not cookies_str:
            print("❌ Cookie未配置，请先配置Cookie")
            return False
        print(f"✅ Cookie配置正常 (长度: {len(cookies_str)})")
        
        api_key = main.config.ai.api_key
        if not api_key:
            print("❌ API_KEY未配置，请先配置API_KEY")
            return False
        print(f"✅ API_KEY配置正常")
        
        # 直接调用run_service函数
        print("\n2️⃣ 直接启动服务...")
        print("注意：这将启动实际的WebSocket连接，按Ctrl+C停止")
        
        # 调用服务启动函数
        success = main.run_service()
        
        if success:
            print("✅ 服务启动成功")
        else:
            print("❌ 服务启动失败")
            
        return success
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，测试停止")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_components_only():
    """仅测试组件初始化，不启动WebSocket"""
    print("🔧 测试组件初始化...")
    
    try:
        # 加载环境变量
        load_dotenv()
        
        # 导入主模块
        print("📦 导入main模块...")
        import main
        
        # 1. 测试配置
        print("\n1️⃣ 测试配置...")
        cookies_str = main.config.security.cookies_str
        if not cookies_str:
            print("❌ Cookie未配置")
            return False
        print(f"✅ Cookie配置正常 (长度: {len(cookies_str)})")
        
        # 2. 测试AI客户端
        print("\n2️⃣ 测试AI客户端...")
        ai_manager = main.AIClientManager()
        test_client = ai_manager.client
        print("✅ AI客户端初始化成功")
        
        # 3. 测试Bot初始化
        print("\n3️⃣ 测试Bot初始化...")
        try:
            smart_bot = main.SmartXianyuAgent()
            print("✅ 智能代理初始化成功")
        except Exception as e:
            print(f"⚠️ 智能代理初始化失败，尝试传统机器人: {e}")
            traditional_bot = main.XianyuReplyBot()
            print("✅ 传统机器人初始化成功")
        
        # 4. 测试XianyuLive初始化（不启动WebSocket）
        print("\n4️⃣ 测试XianyuLive初始化...")
        xianyu_live = main.XianyuLive(cookies_str)
        print("✅ XianyuLive初始化成功")
        print(f"  用户ID: {xianyu_live.myid}")
        print(f"  设备ID: {xianyu_live.device_id}")
        print(f"  WebSocket URL: {xianyu_live.base_url}")
        
        # 5. 测试AI回复生成
        print("\n5️⃣ 测试AI回复生成...")
        test_message = "你好，这个商品还在吗？"
        test_item_desc = "测试商品;当前商品售卖价格为:100"
        test_context = []
        
        if 'smart_bot' in locals():
            reply = smart_bot.generate_reply(test_message, "test_item_123", test_item_desc, test_context)
            print(f"✅ 智能代理回复: {reply[:100]}...")
        else:
            reply = traditional_bot.generate_reply(test_message, test_item_desc, test_context)
            print(f"✅ 传统机器人回复: {reply[:100]}...")
        
        print("\n🎉 所有组件测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 组件测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 仅测试组件初始化（推荐）")
    print("2. 完整服务启动测试")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        success = test_components_only()
    elif choice == "2":
        success = test_direct_service()
    else:
        print("无效选择，默认执行组件测试")
        success = test_components_only()
    
    print(f"\n{'='*50}")
    print(f"测试结果: {'✅ 成功' if success else '❌ 失败'}")
    input("按回车键退出...")
