#!/usr/bin/env python3
"""
测试集成启动功能
"""

import sys
import os
import subprocess
import time

def test_startup_dialog():
    """测试启动对话框"""
    print("🧪 测试启动对话框...")
    
    try:
        # 测试导入
        import tkinter as tk
        from tkinter import ttk
        print("✅ GUI库导入成功")
        
        # 测试启动对话框函数
        sys.path.append('.')
        
        # 模拟启动对话框的核心逻辑
        from dotenv import load_dotenv
        load_dotenv()
        
        cookies_str = os.getenv("COOKIES_STR")
        if cookies_str:
            print("✅ Cookie配置已就绪")
        else:
            print("⚠️ Cookie配置缺失")
        
        print("✅ 启动对话框测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 启动对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_gui_import():
    """测试配置界面导入"""
    print("🧪 测试配置界面导入...")
    
    try:
        from config_gui import ConfigGUI
        print("✅ 配置界面导入成功")
        return True
    except Exception as e:
        print(f"❌ 配置界面导入失败: {e}")
        return False

def test_main_imports():
    """测试main.py的导入"""
    print("🧪 测试main.py导入...")
    
    try:
        # 测试关键模块导入
        from smart_xianyu_agent import SmartXianyuAgent
        from XianyuAgent import XianyuReplyBot
        print("✅ 智能代理模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ main.py导入测试失败: {e}")
        return False

def test_command_line_args():
    """测试命令行参数"""
    print("🧪 测试命令行参数...")
    
    try:
        # 测试帮助参数
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0 and "XianyuAutoAgent" in result.stdout:
            print("✅ --help 参数测试通过")
            return True
        else:
            print(f"❌ --help 参数测试失败: {result.stdout}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 命令行参数测试超时")
        return False
    except Exception as e:
        print(f"❌ 命令行参数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试集成启动功能...")
    print("=" * 50)
    
    tests = [
        ("启动对话框", test_startup_dialog),
        ("配置界面导入", test_config_gui_import),
        ("主模块导入", test_main_imports),
        ("命令行参数", test_command_line_args),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！")
        print("\n📋 现在您可以：")
        print("1. 运行 'python main.py' 显示启动选择界面")
        print("2. 运行 'python main.py --config' 直接打开配置界面")
        print("3. 运行 'python main.py --service' 直接启动服务")
        print("4. 运行 'python main.py --help' 查看帮助信息")
        
        print("\n🎯 集成功能特点：")
        print("- 🖥️ 友好的图形化启动选择界面")
        print("- ⚙️ 一键打开配置管理界面")
        print("- 🚀 一键启动智能客服服务")
        print("- 📋 实时配置状态检查")
        print("- 🔧 支持命令行参数快速启动")
        
        return 0
    else:
        print(f"\n❌ {total - passed} 个测试失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
