#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能商品信息管理系统
支持通用模式和针对性模式的智能商品信息检测和回复策略管理
"""

import json
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from loguru import logger
import re
import os
from pathlib import Path


@dataclass
class ProductInfo:
    """商品信息数据类"""
    item_id: str
    title: str
    price: float
    original_price: float = 0.0
    description: str = ""
    category: str = ""
    condition: str = ""  # 新旧程度
    location: str = ""
    seller_id: str = ""
    images: List[str] = None
    tags: List[str] = None
    stock_status: str = "available"  # available, sold, reserved
    last_updated: datetime = None
    
    def __post_init__(self):
        if self.images is None:
            self.images = []
        if self.tags is None:
            self.tags = []
        if self.last_updated is None:
            self.last_updated = datetime.now()


@dataclass
class ReplyStrategy:
    """回复策略配置"""
    item_id: str
    mode: str = "general"  # general(通用) 或 specific(针对性)
    
    # 价格策略
    min_price: float = 0.0
    max_discount_percent: float = 10.0  # 最大折扣百分比
    bargain_rounds: int = 3  # 允许议价轮数
    
    # 自定义回复模板
    greeting_template: str = ""
    price_response_template: str = ""
    tech_response_template: str = ""
    final_price_template: str = ""
    
    # 商品特殊说明
    special_notes: str = ""
    selling_points: List[str] = None
    
    # 自动回复开关
    auto_reply_enabled: bool = True
    
    def __post_init__(self):
        if self.selling_points is None:
            self.selling_points = []


class SmartProductManager:
    """智能商品管理器"""
    
    def __init__(self, db_path: str = "data/smart_products.db"):
        self.db_path = db_path
        self.ensure_db_dir()
        self.init_database()
        
    def ensure_db_dir(self):
        """确保数据库目录存在"""
        db_dir = Path(self.db_path).parent
        db_dir.mkdir(parents=True, exist_ok=True)
        
    def init_database(self):
        """初始化数据库"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 商品信息表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                item_id TEXT PRIMARY KEY,
                title TEXT NOT NULL,
                price REAL NOT NULL,
                original_price REAL DEFAULT 0.0,
                description TEXT DEFAULT '',
                category TEXT DEFAULT '',
                condition_status TEXT DEFAULT '',
                location TEXT DEFAULT '',
                seller_id TEXT DEFAULT '',
                images TEXT DEFAULT '[]',
                tags TEXT DEFAULT '[]',
                stock_status TEXT DEFAULT 'available',
                last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # 回复策略表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS reply_strategies (
                item_id TEXT PRIMARY KEY,
                mode TEXT DEFAULT 'general',
                min_price REAL DEFAULT 0.0,
                max_discount_percent REAL DEFAULT 10.0,
                bargain_rounds INTEGER DEFAULT 3,
                greeting_template TEXT DEFAULT '',
                price_response_template TEXT DEFAULT '',
                tech_response_template TEXT DEFAULT '',
                final_price_template TEXT DEFAULT '',
                special_notes TEXT DEFAULT '',
                selling_points TEXT DEFAULT '[]',
                auto_reply_enabled BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (item_id) REFERENCES products (item_id)
            )
            ''')
            
            # 价格历史表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS price_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                item_id TEXT NOT NULL,
                old_price REAL NOT NULL,
                new_price REAL NOT NULL,
                change_reason TEXT DEFAULT '',
                changed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (item_id) REFERENCES products (item_id)
            )
            ''')
            
            conn.commit()
            logger.info(f"智能商品管理数据库初始化完成: {self.db_path}")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            conn.rollback()
            raise
        finally:
            conn.close()
    
    def extract_product_info(self, raw_data: Dict) -> ProductInfo:
        """从原始API数据中提取商品信息"""
        try:
            item_id = str(raw_data.get('id', ''))
            title = raw_data.get('title', raw_data.get('desc', ''))
            price = float(raw_data.get('soldPrice', raw_data.get('price', 0)))
            original_price = float(raw_data.get('originalPrice', price))
            description = raw_data.get('desc', raw_data.get('description', ''))
            
            # 提取更多信息
            category = raw_data.get('category', '')
            condition = raw_data.get('condition', raw_data.get('quality', ''))
            location = raw_data.get('location', raw_data.get('city', ''))
            seller_id = str(raw_data.get('sellerId', raw_data.get('userId', '')))
            
            # 处理图片列表
            images = []
            if 'images' in raw_data:
                if isinstance(raw_data['images'], list):
                    images = raw_data['images']
                elif isinstance(raw_data['images'], str):
                    try:
                        images = json.loads(raw_data['images'])
                    except:
                        images = [raw_data['images']]
            
            # 提取标签
            tags = []
            if 'tags' in raw_data:
                if isinstance(raw_data['tags'], list):
                    tags = raw_data['tags']
                elif isinstance(raw_data['tags'], str):
                    try:
                        tags = json.loads(raw_data['tags'])
                    except:
                        tags = [raw_data['tags']]
            
            # 判断库存状态
            stock_status = "available"
            if raw_data.get('status') == 'sold':
                stock_status = "sold"
            elif raw_data.get('reserved', False):
                stock_status = "reserved"
                
            return ProductInfo(
                item_id=item_id,
                title=title,
                price=price,
                original_price=original_price,
                description=description,
                category=category,
                condition=condition,
                location=location,
                seller_id=seller_id,
                images=images,
                tags=tags,
                stock_status=stock_status
            )
            
        except Exception as e:
            logger.error(f"提取商品信息失败: {e}")
            raise
    
    def save_product(self, product: ProductInfo) -> bool:
        """保存商品信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 检查是否需要记录价格变化
            existing = self.get_product(product.item_id)
            if existing and existing.price != product.price:
                self._record_price_change(cursor, product.item_id, existing.price, product.price)
            
            cursor.execute('''
            INSERT INTO products (
                item_id, title, price, original_price, description, category,
                condition_status, location, seller_id, images, tags, stock_status, last_updated
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(item_id) DO UPDATE SET
                title = excluded.title,
                price = excluded.price,
                original_price = excluded.original_price,
                description = excluded.description,
                category = excluded.category,
                condition_status = excluded.condition_status,
                location = excluded.location,
                seller_id = excluded.seller_id,
                images = excluded.images,
                tags = excluded.tags,
                stock_status = excluded.stock_status,
                last_updated = excluded.last_updated
            ''', (
                product.item_id, product.title, product.price, product.original_price,
                product.description, product.category, product.condition, product.location,
                product.seller_id, json.dumps(product.images), json.dumps(product.tags),
                product.stock_status, product.last_updated.isoformat()
            ))
            
            conn.commit()
            logger.debug(f"商品信息已保存: {product.item_id}")
            return True
            
        except Exception as e:
            logger.error(f"保存商品信息失败: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()
    
    def get_product(self, item_id: str) -> Optional[ProductInfo]:
        """获取商品信息"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
            SELECT item_id, title, price, original_price, description, category,
                   condition_status, location, seller_id, images, tags, stock_status, last_updated
            FROM products WHERE item_id = ?
            ''', (item_id,))
            
            row = cursor.fetchone()
            if not row:
                return None
                
            return ProductInfo(
                item_id=row[0],
                title=row[1],
                price=row[2],
                original_price=row[3],
                description=row[4],
                category=row[5],
                condition=row[6],
                location=row[7],
                seller_id=row[8],
                images=json.loads(row[9]) if row[9] else [],
                tags=json.loads(row[10]) if row[10] else [],
                stock_status=row[11],
                last_updated=datetime.fromisoformat(row[12]) if row[12] else datetime.now()
            )
            
        except Exception as e:
            logger.error(f"获取商品信息失败: {e}")
            return None
        finally:
            conn.close()
    
    def _record_price_change(self, cursor, item_id: str, old_price: float, new_price: float, reason: str = "价格更新"):
        """记录价格变化历史"""
        cursor.execute('''
        INSERT INTO price_history (item_id, old_price, new_price, change_reason)
        VALUES (?, ?, ?, ?)
        ''', (item_id, old_price, new_price, reason))

    def save_reply_strategy(self, strategy: ReplyStrategy) -> bool:
        """保存回复策略"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
            INSERT INTO reply_strategies (
                item_id, mode, min_price, max_discount_percent, bargain_rounds,
                greeting_template, price_response_template, tech_response_template,
                final_price_template, special_notes, selling_points, auto_reply_enabled, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ON CONFLICT(item_id) DO UPDATE SET
                mode = excluded.mode,
                min_price = excluded.min_price,
                max_discount_percent = excluded.max_discount_percent,
                bargain_rounds = excluded.bargain_rounds,
                greeting_template = excluded.greeting_template,
                price_response_template = excluded.price_response_template,
                tech_response_template = excluded.tech_response_template,
                final_price_template = excluded.final_price_template,
                special_notes = excluded.special_notes,
                selling_points = excluded.selling_points,
                auto_reply_enabled = excluded.auto_reply_enabled,
                updated_at = excluded.updated_at
            ''', (
                strategy.item_id, strategy.mode, strategy.min_price, strategy.max_discount_percent,
                strategy.bargain_rounds, strategy.greeting_template, strategy.price_response_template,
                strategy.tech_response_template, strategy.final_price_template, strategy.special_notes,
                json.dumps(strategy.selling_points), strategy.auto_reply_enabled, datetime.now().isoformat()
            ))

            conn.commit()
            logger.debug(f"回复策略已保存: {strategy.item_id}")
            return True

        except Exception as e:
            logger.error(f"保存回复策略失败: {e}")
            conn.rollback()
            return False
        finally:
            conn.close()

    def get_reply_strategy(self, item_id: str) -> Optional[ReplyStrategy]:
        """获取回复策略"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        try:
            cursor.execute('''
            SELECT item_id, mode, min_price, max_discount_percent, bargain_rounds,
                   greeting_template, price_response_template, tech_response_template,
                   final_price_template, special_notes, selling_points, auto_reply_enabled
            FROM reply_strategies WHERE item_id = ?
            ''', (item_id,))

            row = cursor.fetchone()
            if not row:
                # 返回默认策略
                return ReplyStrategy(item_id=item_id)

            return ReplyStrategy(
                item_id=row[0],
                mode=row[1],
                min_price=row[2],
                max_discount_percent=row[3],
                bargain_rounds=row[4],
                greeting_template=row[5],
                price_response_template=row[6],
                tech_response_template=row[7],
                final_price_template=row[8],
                special_notes=row[9],
                selling_points=json.loads(row[10]) if row[10] else [],
                auto_reply_enabled=bool(row[11])
            )

        except Exception as e:
            logger.error(f"获取回复策略失败: {e}")
            return ReplyStrategy(item_id=item_id)
        finally:
            conn.close()

    def analyze_product_automatically(self, product: ProductInfo) -> Dict[str, Any]:
        """自动分析商品信息，生成智能建议"""
        analysis = {
            "category_suggestions": [],
            "price_analysis": {},
            "selling_points": [],
            "recommended_strategy": {},
            "keywords": []
        }

        try:
            # 1. 分析商品类别
            title_lower = product.title.lower()
            description_lower = product.description.lower()

            # 电子产品关键词
            if any(kw in title_lower or kw in description_lower for kw in
                   ['手机', '电脑', '笔记本', '平板', '耳机', '音响', '相机', '数码']):
                analysis["category_suggestions"].append("电子产品")
                analysis["selling_points"].extend(["正品保证", "功能完好", "外观良好"])

            # 服装关键词
            if any(kw in title_lower or kw in description_lower for kw in
                   ['衣服', '裤子', '裙子', '鞋子', '包包', '帽子', '围巾']):
                analysis["category_suggestions"].append("服装配饰")
                analysis["selling_points"].extend(["尺码合适", "款式时尚", "质量上乘"])

            # 家居用品
            if any(kw in title_lower or kw in description_lower for kw in
                   ['家具', '装饰', '厨具', '床品', '灯具', '收纳']):
                analysis["category_suggestions"].append("家居用品")
                analysis["selling_points"].extend(["实用性强", "品质优良", "性价比高"])

            # 2. 价格分析
            if product.original_price > 0:
                discount = (product.original_price - product.price) / product.original_price * 100
                analysis["price_analysis"] = {
                    "discount_percent": round(discount, 1),
                    "is_discounted": discount > 0,
                    "price_range": self._get_price_range(product.price)
                }

            # 3. 提取关键词
            analysis["keywords"] = self._extract_keywords(product.title + " " + product.description)

            # 4. 推荐策略
            analysis["recommended_strategy"] = self._recommend_strategy(product, analysis)

        except Exception as e:
            logger.error(f"自动分析商品失败: {e}")

        return analysis

    def _get_price_range(self, price: float) -> str:
        """获取价格区间"""
        if price < 50:
            return "低价位"
        elif price < 200:
            return "中低价位"
        elif price < 500:
            return "中价位"
        elif price < 1000:
            return "中高价位"
        else:
            return "高价位"

    def _extract_keywords(self, text: str) -> List[str]:
        """提取关键词"""
        # 简单的关键词提取，可以后续用更复杂的NLP方法
        keywords = []

        # 品牌关键词
        brands = ['苹果', 'iPhone', '华为', '小米', '三星', 'OPPO', 'vivo', '联想', '戴尔', 'HP']
        for brand in brands:
            if brand in text:
                keywords.append(brand)

        # 状态关键词
        conditions = ['全新', '99新', '95新', '9成新', '8成新', '二手', '闲置']
        for condition in conditions:
            if condition in text:
                keywords.append(condition)

        return list(set(keywords))

    def _recommend_strategy(self, product: ProductInfo, analysis: Dict) -> Dict[str, Any]:
        """推荐回复策略"""
        strategy = {
            "mode": "general",
            "max_discount_percent": 10.0,
            "bargain_rounds": 3,
            "auto_templates": {}
        }

        # 根据价格区间调整策略
        price_range = analysis.get("price_analysis", {}).get("price_range", "")

        if price_range in ["低价位", "中低价位"]:
            strategy["max_discount_percent"] = 5.0  # 低价商品折扣空间小
            strategy["bargain_rounds"] = 2
        elif price_range in ["高价位"]:
            strategy["max_discount_percent"] = 15.0  # 高价商品折扣空间大
            strategy["bargain_rounds"] = 5

        # 生成自动模板
        strategy["auto_templates"] = self._generate_auto_templates(product, analysis)

        return strategy

    def _generate_auto_templates(self, product: ProductInfo, analysis: Dict) -> Dict[str, str]:
        """生成自动回复模板"""
        templates = {}

        # 问候模板
        selling_points = analysis.get("selling_points", [])
        if selling_points:
            templates["greeting"] = f"您好！这个{product.title}确实不错，{', '.join(selling_points[:2])}，有什么问题可以随时问我哦~"
        else:
            templates["greeting"] = f"您好！感谢您对{product.title}的关注，有什么问题可以随时问我~"

        # 价格回复模板
        if product.original_price > product.price:
            discount = analysis.get("price_analysis", {}).get("discount_percent", 0)
            templates["price"] = f"这个价格已经很优惠了，比原价便宜了{discount}%，质量和性价比都很不错的~"
        else:
            templates["price"] = f"这个价格是我精心定的，性价比很高，您觉得怎么样？"

        return templates
