"""
配置管理模块
提供统一的配置管理、验证和热重载功能
"""

import os
import json
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path
from loguru import logger
from dotenv import load_dotenv


@dataclass
class DatabaseConfig:
    """数据库配置"""
    path: str = "data/chat_history.db"
    max_history: int = 100
    backup_interval: int = 3600  # 备份间隔（秒）
    
    def __post_init__(self):
        """确保数据库目录存在"""
        db_dir = Path(self.path).parent
        db_dir.mkdir(parents=True, exist_ok=True)


@dataclass
class WebSocketConfig:
    """WebSocket配置"""
    base_url: str = 'wss://wss-goofish.dingtalk.com/'
    heartbeat_interval: int = 15
    heartbeat_timeout: int = 5
    token_refresh_interval: int = 3600
    token_retry_interval: int = 300
    message_expire_time: int = 300000
    
    def validate(self) -> bool:
        """验证配置有效性"""
        if self.heartbeat_interval <= 0:
            raise ValueError("心跳间隔必须大于0")
        if self.heartbeat_timeout <= 0:
            raise ValueError("心跳超时必须大于0")
        return True


@dataclass
class AIConfig:
    """AI配置"""
    api_key: str = ""
    base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    model_name: str = "qwen-max"
    temperature: float = 0.4
    max_tokens: int = 500
    top_p: float = 0.8
    
    def validate(self) -> bool:
        """验证AI配置"""
        if not self.api_key:
            raise ValueError("API_KEY不能为空")
        if self.temperature < 0 or self.temperature > 2:
            raise ValueError("temperature必须在0-2之间")
        return True


@dataclass
class SecurityConfig:
    """安全配置"""
    cookies_str: str = ""
    toggle_keywords: str = "。"
    manual_mode_timeout: int = 3600
    max_login_attempts: int = 3
    session_timeout: int = 7200
    
    def validate(self) -> bool:
        """验证安全配置"""
        if not self.cookies_str:
            raise ValueError("COOKIES_STR不能为空")
        return True


@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    rotation: str = "100 MB"
    retention: str = "30 days"
    file_path: str = "logs/xianyu_agent.log"
    
    def validate(self) -> bool:
        """验证日志配置"""
        valid_levels = ["TRACE", "DEBUG", "INFO", "SUCCESS", "WARNING", "ERROR", "CRITICAL"]
        if self.level.upper() not in valid_levels:
            raise ValueError(f"日志级别必须是: {valid_levels}")
        
        # 确保日志目录存在
        log_dir = Path(self.file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        return True


class ConfigManager:
    """统一配置管理器"""
    
    def __init__(self, env_file: str = ".env"):
        self.env_file = env_file
        self._config_cache: Dict[str, Any] = {}
        self.load_config()
    
    def load_config(self) -> None:
        """加载配置"""
        try:
            # 加载环境变量
            load_dotenv(self.env_file)
            
            # 初始化各模块配置
            self.database = DatabaseConfig(
                path=os.getenv("DB_PATH", "data/chat_history.db"),
                max_history=int(os.getenv("MAX_HISTORY", "100")),
                backup_interval=int(os.getenv("BACKUP_INTERVAL", "3600"))
            )
            
            self.websocket = WebSocketConfig(
                base_url=os.getenv("WEBSOCKET_URL", "wss://wss-goofish.dingtalk.com/"),
                heartbeat_interval=int(os.getenv("HEARTBEAT_INTERVAL", "15")),
                heartbeat_timeout=int(os.getenv("HEARTBEAT_TIMEOUT", "5")),
                token_refresh_interval=int(os.getenv("TOKEN_REFRESH_INTERVAL", "3600")),
                token_retry_interval=int(os.getenv("TOKEN_RETRY_INTERVAL", "300")),
                message_expire_time=int(os.getenv("MESSAGE_EXPIRE_TIME", "300000"))
            )
            
            self.ai = AIConfig(
                api_key=os.getenv("API_KEY", ""),
                base_url=os.getenv("MODEL_BASE_URL", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
                model_name=os.getenv("MODEL_NAME", "qwen-max"),
                temperature=float(os.getenv("AI_TEMPERATURE", "0.4")),
                max_tokens=int(os.getenv("AI_MAX_TOKENS", "500")),
                top_p=float(os.getenv("AI_TOP_P", "0.8"))
            )
            
            self.security = SecurityConfig(
                cookies_str=os.getenv("COOKIES_STR", ""),
                toggle_keywords=os.getenv("TOGGLE_KEYWORDS", "。"),
                manual_mode_timeout=int(os.getenv("MANUAL_MODE_TIMEOUT", "3600")),
                max_login_attempts=int(os.getenv("MAX_LOGIN_ATTEMPTS", "3")),
                session_timeout=int(os.getenv("SESSION_TIMEOUT", "7200"))
            )
            
            self.log = LogConfig(
                level=os.getenv("LOG_LEVEL", "INFO").upper(),
                format=os.getenv("LOG_FORMAT", self.log.format if hasattr(self, 'log') else LogConfig().format),
                rotation=os.getenv("LOG_ROTATION", "100 MB"),
                retention=os.getenv("LOG_RETENTION", "30 days"),
                file_path=os.getenv("LOG_FILE_PATH", "logs/xianyu_agent.log")
            )
            
            # 验证所有配置
            self.validate_all()
            
            logger.info("配置加载成功")
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise
    
    def validate_all(self) -> None:
        """验证所有配置"""
        try:
            self.websocket.validate()
            self.ai.validate()
            self.security.validate()
            self.log.validate()
            logger.debug("所有配置验证通过")
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            raise
    
    def reload_config(self) -> None:
        """重新加载配置"""
        logger.info("重新加载配置...")
        self.load_config()
        logger.info("配置重新加载完成")
    
    def get_config_dict(self) -> Dict[str, Any]:
        """获取配置字典"""
        return {
            "database": self.database.__dict__,
            "websocket": self.websocket.__dict__,
            "ai": self.ai.__dict__,
            "security": {k: v if k != "cookies_str" else "***" for k, v in self.security.__dict__.items()},
            "log": self.log.__dict__
        }
    
    def update_config(self, section: str, key: str, value: Any) -> None:
        """更新配置项"""
        if hasattr(self, section):
            config_obj = getattr(self, section)
            if hasattr(config_obj, key):
                setattr(config_obj, key, value)
                logger.info(f"配置已更新: {section}.{key} = {value}")
            else:
                raise ValueError(f"配置项不存在: {section}.{key}")
        else:
            raise ValueError(f"配置节不存在: {section}")


# 全局配置实例
config = ConfigManager()
