#!/usr/bin/env python3
"""
配置检查脚本
用于验证配置是否正确设置
"""

import os
import sys
from pathlib import Path

def check_env_file():
    """检查.env文件是否存在"""
    env_file = Path(".env")
    if not env_file.exists():
        print("❌ .env文件不存在")
        print("📝 请复制.env.example为.env并填入配置")
        return False
    else:
        print("✅ .env文件存在")
        return True

def check_required_configs():
    """检查必需的配置项"""
    from dotenv import load_dotenv
    load_dotenv()
    
    required_configs = {
        "API_KEY": "AI服务API密钥",
        "COOKIES_STR": "闲鱼Cookie字符串"
    }
    
    missing_configs = []
    
    for key, description in required_configs.items():
        value = os.getenv(key)
        if not value or value.strip() == "" or "your_" in value.lower():
            missing_configs.append(f"{key} ({description})")
            print(f"❌ {key}: 未配置或使用默认值")
        else:
            print(f"✅ {key}: 已配置")
    
    return missing_configs

def check_dependencies():
    """检查依赖包是否安装"""
    required_packages = [
        "openai",
        "websockets", 
        "loguru",
        "python-dotenv",
        "requests",
        "cryptography"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}: 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}: 未安装")
    
    return missing_packages

def check_directories():
    """检查必要的目录"""
    directories = ["data", "logs", "prompts"]
    
    for directory in directories:
        dir_path = Path(directory)
        if dir_path.exists():
            print(f"✅ {directory}/: 目录存在")
        else:
            print(f"⚠️  {directory}/: 目录不存在，将自动创建")
            dir_path.mkdir(parents=True, exist_ok=True)

def check_prompt_files():
    """检查提示词文件"""
    prompt_files = [
        "prompts/classify_prompt.txt",
        "prompts/price_prompt.txt", 
        "prompts/tech_prompt.txt",
        "prompts/default_prompt.txt"
    ]
    
    missing_files = []
    
    for file_path in prompt_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}: 文件存在")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path}: 文件不存在")
    
    return missing_files

def test_config_loading():
    """测试配置加载"""
    try:
        print("\n🔧 测试配置加载...")
        from config import config
        print("✅ 配置模块加载成功")
        
        # 显示配置摘要
        print(f"   - 数据库路径: {config.database.path}")
        print(f"   - 日志级别: {config.log.level}")
        print(f"   - WebSocket URL: {config.websocket.base_url}")
        
        return True
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def main():
    """主检查函数"""
    print("🔍 XianyuAutoAgent 配置检查")
    print("=" * 50)
    
    # 检查.env文件
    print("\n📁 检查配置文件...")
    env_exists = check_env_file()
    
    # 检查必需配置
    print("\n⚙️  检查必需配置...")
    missing_configs = check_required_configs() if env_exists else []
    
    # 检查依赖包
    print("\n📦 检查依赖包...")
    missing_packages = check_dependencies()
    
    # 检查目录
    print("\n📂 检查目录结构...")
    check_directories()
    
    # 检查提示词文件
    print("\n📝 检查提示词文件...")
    missing_prompt_files = check_prompt_files()
    
    # 测试配置加载
    config_loaded = test_config_loading()
    
    # 总结
    print("\n" + "=" * 50)
    print("📋 检查总结")
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("   请运行: pip install -r requirements.txt")
    
    if missing_configs:
        print(f"❌ 缺少必需配置: {', '.join(missing_configs)}")
        print("   请编辑.env文件并填入正确的配置值")
    
    if missing_prompt_files:
        print(f"❌ 缺少提示词文件: {', '.join(missing_prompt_files)}")
        print("   请确保prompts目录下有所有必需的提示词文件")
    
    if not missing_packages and not missing_configs and not missing_prompt_files and config_loaded:
        print("🎉 所有检查通过！可以运行程序了")
        print("   运行命令: python main.py")
        return 0
    else:
        print("⚠️  存在配置问题，请根据上述提示修复")
        return 1

if __name__ == "__main__":
    sys.exit(main())
