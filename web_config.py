#!/usr/bin/env python3
"""
Web配置管理界面
提供友好的图形化配置体验
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, Optional
from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from dotenv import load_dotenv, set_key
from loguru import logger

# 导入配置模块
try:
    from config import config
except ImportError:
    config = None

app = Flask(__name__)
app.secret_key = 'xianyu_config_secret_key_2024'

class WebConfigManager:
    """Web配置管理器"""
    
    def __init__(self):
        self.env_file = ".env"
        self.prompts_dir = Path("prompts")
        self.ensure_directories()
    
    def ensure_directories(self):
        """确保必要的目录存在"""
        self.prompts_dir.mkdir(exist_ok=True)
        Path("data").mkdir(exist_ok=True)
        Path("logs").mkdir(exist_ok=True)
    
    def load_env_config(self) -> Dict[str, str]:
        """加载环境配置"""
        load_dotenv(self.env_file)
        
        config_data = {
            # AI配置
            'API_KEY': os.getenv('API_KEY', ''),
            'BASE_URL': os.getenv('BASE_URL', 'https://api.openai.com/v1'),
            'MODEL': os.getenv('MODEL', 'gpt-4o-mini'),
            'TEMPERATURE': os.getenv('TEMPERATURE', '0.7'),
            'MAX_TOKENS': os.getenv('MAX_TOKENS', '2000'),
            
            # 安全配置
            'COOKIES_STR': os.getenv('COOKIES_STR', ''),
            'ENCRYPTION_KEY': os.getenv('ENCRYPTION_KEY', ''),
            
            # WebSocket配置
            'WS_BASE_URL': os.getenv('WS_BASE_URL', 'wss://h5api.m.taobao.com/h5/mtop.taobao.idle.im.ws.connect/1.0/'),
            'WS_TIMEOUT': os.getenv('WS_TIMEOUT', '30'),
            
            # 数据库配置
            'DATABASE_PATH': os.getenv('DATABASE_PATH', 'data/chat_history.db'),
            
            # 日志配置
            'LOG_LEVEL': os.getenv('LOG_LEVEL', 'INFO'),
            'LOG_FILE_PATH': os.getenv('LOG_FILE_PATH', 'logs/xianyu_agent.log'),
            'LOG_ROTATION': os.getenv('LOG_ROTATION', '100 MB'),
            'LOG_RETENTION': os.getenv('LOG_RETENTION', '30 days'),
        }
        
        return config_data
    
    def save_env_config(self, config_data: Dict[str, str]) -> bool:
        """保存环境配置"""
        try:
            # 确保.env文件存在
            if not os.path.exists(self.env_file):
                Path(self.env_file).touch()
            
            # 更新配置
            for key, value in config_data.items():
                set_key(self.env_file, key, value)
            
            logger.info("配置保存成功")
            return True
            
        except Exception as e:
            logger.error(f"配置保存失败: {e}")
            return False
    
    def load_prompts(self) -> Dict[str, str]:
        """加载提示词"""
        prompts = {}
        
        prompt_files = {
            'classify_prompt': 'classify_prompt.txt',
            'price_prompt': 'price_prompt.txt', 
            'tech_prompt': 'tech_prompt.txt',
            'default_prompt': 'default_prompt.txt'
        }
        
        for key, filename in prompt_files.items():
            file_path = self.prompts_dir / filename
            try:
                if file_path.exists():
                    prompts[key] = file_path.read_text(encoding='utf-8')
                else:
                    prompts[key] = self.get_default_prompt(key)
            except Exception as e:
                logger.warning(f"读取提示词文件失败 {filename}: {e}")
                prompts[key] = self.get_default_prompt(key)
        
        return prompts
    
    def save_prompts(self, prompts: Dict[str, str]) -> bool:
        """保存提示词"""
        try:
            prompt_files = {
                'classify_prompt': 'classify_prompt.txt',
                'price_prompt': 'price_prompt.txt',
                'tech_prompt': 'tech_prompt.txt', 
                'default_prompt': 'default_prompt.txt'
            }
            
            for key, filename in prompt_files.items():
                if key in prompts:
                    file_path = self.prompts_dir / filename
                    file_path.write_text(prompts[key], encoding='utf-8')
            
            logger.info("提示词保存成功")
            return True
            
        except Exception as e:
            logger.error(f"提示词保存失败: {e}")
            return False
    
    def get_default_prompt(self, prompt_type: str) -> str:
        """获取默认提示词"""
        defaults = {
            'classify_prompt': """你是一个智能客服助手，需要对用户消息进行分类。

请根据用户消息内容，判断属于以下哪种类型：
1. price - 价格相关（询问价格、议价、讨价还价等）
2. tech - 技术相关（产品功能、使用方法、技术参数等）
3. general - 一般咨询（其他问题）

只需要返回分类结果：price、tech 或 general""",

            'price_prompt': """你是一个专业的销售助手，擅长处理价格相关的咨询。

请根据以下原则回复：
1. 保持友好和专业的语气
2. 如果用户议价，可以适当让步但要保持合理利润
3. 强调产品的价值和优势
4. 回复要简洁明了，不超过100字

用户消息：{user_message}
商品信息：{item_info}

请回复：""",

            'tech_prompt': """你是一个技术支持专家，专门解答产品相关的技术问题。

请根据以下原则回复：
1. 提供准确的技术信息
2. 用通俗易懂的语言解释
3. 如果不确定，建议用户咨询专业人士
4. 回复要详细但不冗长

用户消息：{user_message}
商品信息：{item_info}

请回复：""",

            'default_prompt': """你是一个友好的客服助手，负责回答用户的各种咨询。

请根据以下原则回复：
1. 保持礼貌和专业
2. 根据商品信息提供准确回答
3. 如果无法回答，诚实告知并建议其他解决方案
4. 回复要简洁明了

用户消息：{user_message}
商品信息：{item_info}

请回复："""
        }
        
        return defaults.get(prompt_type, "")
    
    def validate_config(self, config_data: Dict[str, str]) -> Dict[str, str]:
        """验证配置"""
        errors = {}
        
        # 验证必需字段
        if not config_data.get('API_KEY'):
            errors['API_KEY'] = 'API密钥不能为空'
        
        if not config_data.get('COOKIES_STR'):
            errors['COOKIES_STR'] = 'Cookie字符串不能为空'
        
        # 验证数值字段
        try:
            temp = float(config_data.get('TEMPERATURE', '0.7'))
            if temp < 0 or temp > 2:
                errors['TEMPERATURE'] = '温度值必须在0-2之间'
        except ValueError:
            errors['TEMPERATURE'] = '温度值必须是数字'
        
        try:
            tokens = int(config_data.get('MAX_TOKENS', '2000'))
            if tokens < 1 or tokens > 8000:
                errors['MAX_TOKENS'] = '最大令牌数必须在1-8000之间'
        except ValueError:
            errors['MAX_TOKENS'] = '最大令牌数必须是整数'
        
        return errors

# 创建全局配置管理器实例
web_config_manager = WebConfigManager()

@app.route('/')
def index():
    """主页"""
    return render_template('config.html')

@app.route('/api/config', methods=['GET'])
def get_config():
    """获取配置"""
    try:
        env_config = web_config_manager.load_env_config()
        prompts = web_config_manager.load_prompts()
        
        return jsonify({
            'success': True,
            'env_config': env_config,
            'prompts': prompts
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/config', methods=['POST'])
def save_config():
    """保存配置"""
    try:
        data = request.get_json()
        
        if 'env_config' in data:
            # 验证配置
            errors = web_config_manager.validate_config(data['env_config'])
            if errors:
                return jsonify({
                    'success': False,
                    'errors': errors
                }), 400
            
            # 保存环境配置
            if not web_config_manager.save_env_config(data['env_config']):
                return jsonify({
                    'success': False,
                    'error': '环境配置保存失败'
                }), 500
        
        if 'prompts' in data:
            # 保存提示词
            if not web_config_manager.save_prompts(data['prompts']):
                return jsonify({
                    'success': False,
                    'error': '提示词保存失败'
                }), 500
        
        return jsonify({
            'success': True,
            'message': '配置保存成功'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/test-config', methods=['POST'])
def test_config():
    """测试配置"""
    try:
        # 重新加载配置
        if config:
            config.reload_config()
        
        # 这里可以添加更多的配置测试逻辑
        return jsonify({
            'success': True,
            'message': '配置测试通过'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': f'配置测试失败: {str(e)}'
        }), 500

if __name__ == '__main__':
    print("🌐 启动Web配置管理界面...")
    print("📱 访问地址: http://localhost:5000")
    print("🔧 在浏览器中配置您的XianyuAutoAgent设置")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
