#!/usr/bin/env python3
"""
简单测试main.py的集成功能
"""

import sys
import os

def test_imports():
    """测试导入"""
    print("测试导入...")
    
    try:
        # 测试tkinter
        import tkinter as tk
        print("✅ tkinter导入成功")
        
        # 测试主要模块
        from config_gui import ConfigGUI
        print("✅ config_gui导入成功")
        
        from smart_xianyu_agent import SmartXianyuAgent
        print("✅ smart_xianyu_agent导入成功")
        
        from XianyuAgent import XianyuReplyBot
        print("✅ XianyuAgent导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_startup_dialog_function():
    """测试启动对话框函数"""
    print("测试启动对话框函数...")
    
    try:
        # 导入main模块中的函数
        sys.path.insert(0, '.')
        
        # 测试环境变量加载
        from dotenv import load_dotenv
        load_dotenv()
        
        cookies_str = os.getenv("COOKIES_STR")
        if cookies_str:
            print("✅ Cookie配置存在")
        else:
            print("⚠️ Cookie配置不存在")
        
        print("✅ 启动对话框函数测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 启动对话框函数测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始简单测试...")
    
    success = True
    
    if not test_imports():
        success = False
    
    if not test_startup_dialog_function():
        success = False
    
    if success:
        print("\n🎉 基本测试通过！")
        print("\n📋 集成功能已就绪:")
        print("- 运行 'python main.py' 显示启动选择界面")
        print("- 运行 'python main.py --config' 直接打开配置界面")
        print("- 运行 'python main.py --service' 直接启动服务")
        print("- 运行 'python main.py --help' 查看帮助信息")
    else:
        print("\n❌ 测试失败！")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
