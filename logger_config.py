"""
日志配置模块
提供统一的日志配置和管理功能
"""

import sys
import os
from pathlib import Path
from loguru import logger
from config import config


def setup_logger():
    """配置日志系统"""
    try:
        # 移除默认的日志处理器
        logger.remove()
        
        # 确保日志目录存在
        log_dir = Path(config.log.file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 添加控制台输出
        logger.add(
            sys.stdout,
            format=config.log.format,
            level=config.log.level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )
        
        # 添加文件输出
        logger.add(
            config.log.file_path,
            format=config.log.format,
            level=config.log.level,
            rotation=config.log.rotation,
            retention=config.log.retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )
        
        # 添加错误日志文件
        error_log_path = str(Path(config.log.file_path).with_suffix('.error.log'))
        logger.add(
            error_log_path,
            format=config.log.format,
            level="ERROR",
            rotation=config.log.rotation,
            retention=config.log.retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )
        
        logger.info("日志系统初始化完成")
        logger.info(f"日志级别: {config.log.level}")
        logger.info(f"日志文件: {config.log.file_path}")
        logger.info(f"错误日志: {error_log_path}")
        
    except Exception as e:
        print(f"日志系统初始化失败: {e}")
        raise


def get_logger(name: str = None):
    """获取日志记录器"""
    if name:
        return logger.bind(name=name)
    return logger


# 在模块导入时自动配置日志
setup_logger()
