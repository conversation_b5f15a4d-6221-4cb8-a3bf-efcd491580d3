#!/usr/bin/env python3
"""
简化的AI连接测试
"""

import os
from dotenv import load_dotenv

def test_basic_ai():
    """基础AI连接测试"""
    print("🔍 基础AI连接测试...")

    try:
        # 加载环境变量
        load_dotenv()

        # 检查基本配置
        api_key = os.getenv("API_KEY", "")
        base_url = os.getenv("BASE_URL", "https://api.deepseek.com/v1")
        model = os.getenv("MODEL", "deepseek-chat")

        print(f"📋 配置检查:")
        print(f"  API_KEY: {'✅ 已配置' if api_key else '❌ 未配置'}")
        print(f"  BASE_URL: {base_url}")
        print(f"  MODEL: {model}")

        if not api_key:
            print("❌ API_KEY未配置")
            return False

        # 测试OpenAI客户端
        print("\n🤖 测试OpenAI客户端...")
        from openai import OpenAI

        client = OpenAI(
            api_key=api_key,
            base_url=base_url,
            timeout=30.0,
            max_retries=3
        )
        print("✅ OpenAI客户端初始化成功")

        # 测试简单调用
        print("\n💬 测试AI调用...")
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "system", "content": "你是一个友好的助手。"},
                {"role": "user", "content": "你好"}
            ],
            temperature=0.7,
            max_tokens=50
        )

        reply = response.choices[0].message.content.strip()
        print(f"✅ AI回复: {reply}")

        print("\n🎉 基础AI连接测试成功！")
        return True

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_basic_ai()
