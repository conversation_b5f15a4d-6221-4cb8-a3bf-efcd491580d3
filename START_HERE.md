# 🚀 XianyuAutoAgent 快速启动指南

## 🎯 方式一：一键启动（最简单）

```bash
# 安装依赖并启动
pip install -r requirements.txt
python start.py
```

启动器会自动：
- ✅ 检查依赖是否安装
- ✅ 检查配置是否完整
- ✅ 提供操作菜单选择
- ✅ 自动打开配置界面（如需要）

## 🖥️ 方式二：Python桌面配置界面

### 最简单的配置方式
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 启动桌面配置界面
python config_gui.py
```

Python桌面界面的优势：
- 🖥️ 原生桌面应用，无需浏览器
- 🔧 友好的图形界面配置所有设置
- 📝 可视化编辑AI提示词模板
- 🧪 内置配置测试功能
- 💾 一键保存所有配置
- 🚀 更好的集成性和性能

## 📋 方式二：传统配置方式（适合高级用户）

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 配置环境变量
编辑 `.env` 文件，填入以下必需配置：

```env
# 必需配置
API_KEY=你的OpenAI_API密钥
COOKIES_STR=你的闲鱼Cookie字符串

# 可选配置（已有默认值）
MODEL=gpt-4o-mini
TEMPERATURE=0.7
LOG_LEVEL=INFO
```

### 3. 获取必需配置

#### 获取 API_KEY
- 访问 [OpenAI API](https://platform.openai.com/api-keys)
- 创建新的API密钥
- 复制密钥到 `.env` 文件中的 `API_KEY=`

#### 获取 COOKIES_STR
1. 打开浏览器，登录闲鱼网站
2. 按F12打开开发者工具
3. 切换到Network标签
4. 刷新页面
5. 找到任意请求，复制Cookie头部的值
6. 粘贴到 `.env` 文件中的 `COOKIES_STR=`

### 4. 测试配置
```bash
# 检查配置状态
python check_config.py
```

### 5. 运行程序
```bash
python main.py
```

## 🔧 故障排除

### 常见问题

1. **配置验证失败**
   - 检查 `.env` 文件是否存在
   - 确认 `API_KEY` 和 `COOKIES_STR` 已正确填写

2. **模块导入错误**
   - 运行 `pip install -r requirements.txt` 安装依赖
   - 检查Python版本（建议3.8+）

3. **数据库错误**
   - 确保 `data/` 目录有写入权限
   - 删除 `data/chat_history.db` 重新初始化

4. **日志错误**
   - 确保 `logs/` 目录有写入权限
   - 检查磁盘空间

### 测试脚本

- `check_config.py` - 配置检查和验证工具

## 📁 目录结构

```
XianyuAutoAgent/
├── main.py              # 主程序入口
├── config.py            # 配置管理
├── exceptions.py        # 异常处理
├── security.py          # 安全功能
├── logger_config.py     # 日志配置
├── context_manager.py   # 聊天上下文管理
├── XianyuAgent.py       # AI代理
├── .env                 # 环境配置
├── requirements.txt     # 依赖列表
├── data/               # 数据目录
├── logs/               # 日志目录
└── prompts/            # 提示词目录
```

## 🎯 功能特性

- ✅ 智能客服回复
- ✅ 上下文记忆
- ✅ 议价功能
- ✅ 安全加密
- ✅ 详细日志
- ✅ 配置管理
- ✅ 错误处理

## 📞 获取帮助

如果遇到问题：
1. 运行 `python check_config.py` 检查配置
2. 查看 `logs/` 目录下的日志文件
3. 检查 `UPGRADE_NOTES.md` 了解详细变更

祝使用愉快！🎉
