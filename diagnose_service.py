#!/usr/bin/env python3
"""
诊断服务启动问题
"""

import os
import sys
import subprocess
import time
from pathlib import Path
from dotenv import load_dotenv

def diagnose_service():
    """诊断服务启动问题"""
    print("🔍 诊断服务启动问题...")
    
    try:
        # 加载环境变量
        load_dotenv()
        
        # 检查基本配置
        print("\n📋 配置检查:")
        api_key = os.getenv("API_KEY", "")
        base_url = os.getenv("BASE_URL", "")
        model = os.getenv("MODEL", "")
        cookies = os.getenv("COOKIES_STR", "")
        
        print(f"  API_KEY: {'✅ 已配置' if api_key else '❌ 未配置'}")
        print(f"  BASE_URL: {base_url}")
        print(f"  MODEL: {model}")
        print(f"  COOKIES: {'✅ 已配置' if cookies else '❌ 未配置'}")
        
        if not api_key:
            print("❌ API_KEY未配置，无法启动服务")
            return False
            
        if not cookies:
            print("❌ COOKIES_STR未配置，无法启动服务")
            return False
        
        # 检查文件存在性
        print("\n📁 文件检查:")
        files_to_check = ["main.py", "config_gui.py", "smart_product_manager.py", "XianyuApis.py"]
        for file in files_to_check:
            exists = Path(file).exists()
            print(f"  {file}: {'✅ 存在' if exists else '❌ 不存在'}")
            if not exists:
                print(f"❌ 关键文件 {file} 不存在")
                return False
        
        # 测试导入主模块
        print("\n🔧 模块导入测试:")
        try:
            import main
            print("✅ main.py 导入成功")
        except Exception as e:
            print(f"❌ main.py 导入失败: {e}")
            return False
        
        # 测试配置管理器
        try:
            config = main.config
            print("✅ 配置管理器初始化成功")
        except Exception as e:
            print(f"❌ 配置管理器初始化失败: {e}")
            return False
        
        # 测试AI客户端
        print("\n🤖 AI客户端测试:")
        try:
            ai_manager = main.AIClientManager()
            client = ai_manager.client
            print("✅ AI客户端初始化成功")
        except Exception as e:
            print(f"❌ AI客户端初始化失败: {e}")
            return False
        
        # 测试智能代理初始化
        print("\n🧠 智能代理测试:")
        try:
            smart_agent = main.SmartXianyuAgent()
            print("✅ 智能代理初始化成功")
        except Exception as e:
            print(f"❌ 智能代理初始化失败: {e}")
            print(f"详细错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        # 测试服务启动函数
        print("\n🚀 服务启动函数测试:")
        try:
            # 不实际运行，只测试函数是否可调用
            run_service = getattr(main, 'run_service', None)
            if run_service:
                print("✅ run_service 函数存在")
            else:
                print("❌ run_service 函数不存在")
                return False
        except Exception as e:
            print(f"❌ 服务启动函数测试失败: {e}")
            return False
        
        print("\n🎉 所有诊断测试通过！")
        print("\n💡 建议:")
        print("1. 尝试直接运行: python main.py --service")
        print("2. 检查新打开的控制台窗口是否有错误信息")
        print("3. 查看 logs/xianyu_agent.log 文件中的详细日志")
        
        return True
        
    except Exception as e:
        print(f"❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_service():
    """测试直接启动服务"""
    print("\n🧪 测试直接启动服务...")
    
    try:
        # 直接调用服务启动函数
        import main
        
        print("正在启动服务...")
        # 这里不实际运行，只是测试能否调用
        print("✅ 服务启动函数可以调用")
        print("💡 如果要实际启动服务，请运行: python main.py --service")
        
    except Exception as e:
        print(f"❌ 直接启动服务失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    success = diagnose_service()
    if success:
        test_direct_service()
    
    input("\n按回车键退出...")
