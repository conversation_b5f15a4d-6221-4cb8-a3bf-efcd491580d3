#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能闲鱼代理
集成智能商品管理和回复生成功能
"""

import re
from typing import List, Dict, Optional, Any
import os
from openai import OpenAI
from loguru import logger
from main import config, AIServiceError, ConfigurationError, handle_exceptions, error_handler, security_manager
from smart_product_manager import SmartProductManager, ProductInfo, ReplyStrategy
from smart_reply_generator import SmartReplyGenerator
from XianyuAgent import XianyuReplyBot, IntentRouter


class SmartXianyuAgent:
    """智能闲鱼代理 - 集成智能商品管理功能"""
    
    def __init__(self):
        """初始化智能代理"""
        try:
            # 初始化智能商品管理器
            self.product_manager = SmartProductManager()
            
            # 初始化智能回复生成器
            self.smart_reply_generator = SmartReplyGenerator(self.product_manager)
            
            # 初始化传统回复机器人（作为备用）
            self.traditional_bot = XianyuReplyBot()
            
            # 全局模式设置
            self.global_mode = "smart"  # smart(智能模式) 或 traditional(传统模式)
            
            logger.info("SmartXianyuAgent初始化完成")
            
        except Exception as e:
            logger.error(f"SmartXianyuAgent初始化失败: {e}")
            raise
    
    def generate_reply(self, user_msg: str, item_id: str, item_desc: str, context: List[Dict]) -> str:
        """
        生成智能回复
        
        Args:
            user_msg: 用户消息
            item_id: 商品ID
            item_desc: 商品描述（传统格式）
            context: 对话上下文
            
        Returns:
            str: 生成的回复
        """
        try:
            # 检查是否启用智能模式
            if self.global_mode == "smart":
                return self._generate_smart_reply(user_msg, item_id, item_desc, context)
            else:
                # 使用传统模式
                return self.traditional_bot.generate_reply(user_msg, item_desc, context)
                
        except Exception as e:
            logger.error(f"生成回复失败，回退到传统模式: {e}")
            # 出错时回退到传统模式
            return self.traditional_bot.generate_reply(user_msg, item_desc, context)
    
    def _generate_smart_reply(self, user_msg: str, item_id: str, item_desc: str, context: List[Dict]) -> str:
        """生成智能回复"""
        
        # 1. 确保商品信息存在
        product = self.product_manager.get_product(item_id)
        if not product:
            # 尝试从传统描述中解析商品信息
            product = self._parse_product_from_desc(item_id, item_desc)
            if product:
                # 保存到数据库
                self.product_manager.save_product(product)
                logger.info(f"自动创建商品信息: {item_id}")
        
        # 2. 检测意图
        intent = self._detect_intent(user_msg, product, context)
        
        # 3. 获取回复策略
        strategy = self.product_manager.get_reply_strategy(item_id)
        
        # 4. 根据策略模式选择生成方法
        if strategy.mode == "specific" and strategy.auto_reply_enabled:
            # 针对性模式
            return self.smart_reply_generator.generate_reply(user_msg, item_id, context, intent)
        elif strategy.mode == "general":
            # 通用模式
            return self.smart_reply_generator.generate_reply(user_msg, item_id, context, intent)
        else:
            # 回退到传统模式
            return self.traditional_bot.generate_reply(user_msg, item_desc, context)
    
    def _parse_product_from_desc(self, item_id: str, item_desc: str) -> Optional[ProductInfo]:
        """从传统商品描述中解析商品信息"""
        try:
            # 解析价格信息
            price_match = re.search(r'当前商品售卖价格为[:：](\d+(?:\.\d+)?)', item_desc)
            price = float(price_match.group(1)) if price_match else 0.0
            
            # 解析标题（取描述的前半部分作为标题）
            desc_parts = item_desc.split(';')
            title = desc_parts[0] if desc_parts else item_desc[:50]
            
            # 创建商品信息对象
            product = ProductInfo(
                item_id=item_id,
                title=title,
                price=price,
                description=item_desc,
                stock_status="available"
            )
            
            return product
            
        except Exception as e:
            logger.error(f"解析商品信息失败: {e}")
            return None
    
    def _detect_intent(self, user_msg: str, product: Optional[ProductInfo], context: List[Dict]) -> str:
        """检测用户意图"""
        try:
            # 使用传统的意图路由器
            router = self.traditional_bot.router
            
            # 构建商品描述
            item_desc = ""
            if product:
                item_desc = f"{product.title};当前商品售卖价格为:{product.price}"
            
            # 格式化上下文
            formatted_context = self.traditional_bot.format_history(context)
            
            # 检测意图
            intent = router.detect(user_msg, item_desc, formatted_context)
            
            # 记录最后意图
            self.traditional_bot.last_intent = intent
            
            return intent
            
        except Exception as e:
            logger.error(f"意图检测失败: {e}")
            return "general"
    
    def update_product_from_api(self, item_id: str, api_data: Dict) -> bool:
        """从API数据更新商品信息"""
        try:
            # 提取商品信息
            product = self.product_manager.extract_product_info(api_data)
            
            # 保存到数据库
            success = self.product_manager.save_product(product)
            
            if success:
                # 自动分析商品并生成建议策略
                analysis = self.product_manager.analyze_product_automatically(product)
                
                # 检查是否需要创建默认策略
                existing_strategy = self.product_manager.get_reply_strategy(item_id)
                if existing_strategy.mode == "general" and not existing_strategy.greeting_template:
                    # 创建智能推荐的策略
                    recommended = analysis.get("recommended_strategy", {})
                    auto_templates = recommended.get("auto_templates", {})
                    
                    new_strategy = ReplyStrategy(
                        item_id=item_id,
                        mode="general",
                        max_discount_percent=recommended.get("max_discount_percent", 10.0),
                        bargain_rounds=recommended.get("bargain_rounds", 3),
                        greeting_template=auto_templates.get("greeting", ""),
                        price_response_template=auto_templates.get("price", ""),
                        selling_points=analysis.get("selling_points", [])
                    )
                    
                    self.product_manager.save_reply_strategy(new_strategy)
                    logger.info(f"自动创建智能策略: {item_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"更新商品信息失败: {e}")
            return False
    
    def set_global_mode(self, mode: str):
        """设置全局模式"""
        if mode in ["smart", "traditional"]:
            self.global_mode = mode
            logger.info(f"全局模式已切换为: {mode}")
        else:
            logger.warning(f"无效的模式: {mode}")
    
    def get_product_info(self, item_id: str) -> Optional[ProductInfo]:
        """获取商品信息"""
        return self.product_manager.get_product(item_id)
    
    def get_reply_strategy(self, item_id: str) -> Optional[ReplyStrategy]:
        """获取回复策略"""
        return self.product_manager.get_reply_strategy(item_id)
    
    def calculate_bargain_price(self, item_id: str, bargain_round: int) -> Optional[float]:
        """计算议价价格"""
        try:
            product = self.product_manager.get_product(item_id)
            strategy = self.product_manager.get_reply_strategy(item_id)
            
            if product and strategy:
                return self.smart_reply_generator.calculate_bargain_price(product, strategy, bargain_round)
            
            return None
            
        except Exception as e:
            logger.error(f"计算议价价格失败: {e}")
            return None
    
    def is_price_acceptable(self, item_id: str, offered_price: float) -> bool:
        """判断用户出价是否可接受"""
        try:
            product = self.product_manager.get_product(item_id)
            strategy = self.product_manager.get_reply_strategy(item_id)
            
            if product and strategy:
                return self.smart_reply_generator.is_price_acceptable(product, strategy, offered_price)
            
            return False
            
        except Exception as e:
            logger.error(f"判断价格可接受性失败: {e}")
            return False
    
    def get_product_analysis(self, item_id: str) -> Optional[Dict]:
        """获取商品分析结果"""
        try:
            product = self.product_manager.get_product(item_id)
            if product:
                return self.product_manager.analyze_product_automatically(product)
            return None
            
        except Exception as e:
            logger.error(f"获取商品分析失败: {e}")
            return None
    
    @property
    def last_intent(self) -> Optional[str]:
        """获取最后检测的意图"""
        return self.traditional_bot.last_intent
    
    def reload_prompts(self):
        """重新加载提示词"""
        self.traditional_bot.reload_prompts()
        logger.info("提示词重新加载完成")
