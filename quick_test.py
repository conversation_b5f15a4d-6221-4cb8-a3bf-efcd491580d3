#!/usr/bin/env python3
"""
快速测试智能商品管理功能
"""

import sys
import os

def test_imports():
    """测试导入"""
    try:
        print("测试导入模块...")
        from smart_product_manager import SmartProductManager, ProductInfo, ReplyStrategy
        print("✓ 智能商品管理模块导入成功")
        
        import tkinter as tk
        from tkinter import ttk, messagebox, scrolledtext
        print("✓ GUI模块导入成功")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_database():
    """测试数据库功能"""
    try:
        print("测试数据库功能...")
        from smart_product_manager import SmartProductManager, ProductInfo
        
        manager = SmartProductManager()
        print("✓ 数据库初始化成功")
        
        # 创建测试商品
        test_product = ProductInfo(
            item_id="quick_test_001",
            title="测试商品",
            price=100.0,
            description="这是一个测试商品"
        )
        
        # 保存商品
        if manager.save_product(test_product):
            print("✓ 商品保存成功")
        else:
            print("✗ 商品保存失败")
            return False
        
        # 获取商品
        retrieved = manager.get_product("quick_test_001")
        if retrieved:
            print(f"✓ 商品获取成功: {retrieved.title}")
        else:
            print("✗ 商品获取失败")
            return False
        
        # 删除商品
        if manager.delete_product("quick_test_001"):
            print("✓ 商品删除成功")
        else:
            print("✗ 商品删除失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 快速测试开始...")
    
    success = True
    
    if not test_imports():
        success = False
    
    if not test_database():
        success = False
    
    if success:
        print("\n🎉 快速测试通过！")
        print("现在可以启动配置界面测试新功能：")
        print("python config_gui.py")
    else:
        print("\n❌ 快速测试失败！")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
