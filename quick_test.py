#!/usr/bin/env python3
"""
全面的AI回复和消息处理Bug测试
"""

import os
import sys
import json
from dotenv import load_dotenv

def comprehensive_test():
    print("🔧 全面测试AI回复和消息处理功能...")

    try:
        # 加载环境变量
        load_dotenv()

        # 导入主模块
        print("📦 导入main模块...")
        import main

        # 1. 测试配置加载
        print("\n1️⃣ 测试配置加载...")
        cookies_str = main.config.security.cookies_str
        if not cookies_str:
            print("❌ Cookie未配置")
            return False
        print(f"✅ Cookie配置正常 (长度: {len(cookies_str)})")

        # 2. 测试AI客户端初始化
        print("\n2️⃣ 测试AI客户端初始化...")
        try:
            ai_manager = main.AIClientManager()
            test_client = ai_manager.client
            print("✅ AI客户端初始化成功")
            print(f"  API_KEY: {'✅ 已配置' if main.config.ai.api_key else '❌ 未配置'}")
            print(f"  BASE_URL: {main.config.ai.base_url}")
            print(f"  MODEL: {main.config.ai.model_name}")
        except Exception as e:
            print(f"❌ AI客户端初始化失败: {e}")
            return False

        # 3. 测试Bot初始化
        print("\n3️⃣ 测试Bot初始化...")
        try:
            # 测试智能代理
            smart_bot = main.SmartXianyuAgent()
            print("✅ 智能代理初始化成功")

            # 测试传统机器人
            traditional_bot = main.XianyuReplyBot()
            print("✅ 传统机器人初始化成功")
        except Exception as e:
            print(f"❌ Bot初始化失败: {e}")
            return False

        # 4. 测试XianyuLive初始化
        print("\n4️⃣ 测试XianyuLive初始化...")
        try:
            xianyu_live = main.XianyuLive(cookies_str)
            print("✅ XianyuLive初始化成功")
            print(f"  用户ID: {xianyu_live.myid}")
            print(f"  设备ID: {xianyu_live.device_id}")
            print(f"  WebSocket URL: {xianyu_live.base_url}")
        except Exception as e:
            print(f"❌ XianyuLive初始化失败: {e}")
            import traceback
            traceback.print_exc()
            return False

        # 5. 测试AI回复生成
        print("\n5️⃣ 测试AI回复生成...")
        try:
            test_message = "你好，这个商品还在吗？"
            test_item_desc = "测试商品;当前商品售卖价格为:100"
            test_context = []

            # 测试智能代理回复
            reply = smart_bot.generate_reply(test_message, "test_item_123", test_item_desc, test_context)
            print(f"✅ 智能代理回复生成成功: {reply[:50]}...")

            # 测试传统机器人回复
            traditional_reply = traditional_bot.generate_reply(test_message, test_item_desc, test_context)
            print(f"✅ 传统机器人回复生成成功: {traditional_reply[:50]}...")

        except Exception as e:
            print(f"❌ AI回复生成测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

        print("\n🎉 所有测试通过！AI回复和消息处理功能正常")
        return True

    except Exception as e:
        print(f"❌ 综合测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = comprehensive_test()
    print(f"\n{'='*50}")
    print(f"测试结果: {'✅ 成功' if success else '❌ 失败'}")
    input("按回车键退出...")
