#!/usr/bin/env python3
"""
快速测试XianyuLive初始化
"""

import os
from dotenv import load_dotenv

def quick_test():
    print("🔧 快速测试XianyuLive初始化...")
    
    try:
        # 加载环境变量
        load_dotenv()
        
        # 导入主模块
        print("导入main模块...")
        import main
        
        # 获取Cookie
        cookies_str = main.config.security.cookies_str
        if not cookies_str:
            print("❌ Cookie未配置")
            return False
        
        print(f"Cookie长度: {len(cookies_str)}")
        
        # 测试XianyuLive初始化
        print("初始化XianyuLive...")
        xianyu_live = main.XianyuLive(cookies_str)
        
        print("✅ XianyuLive初始化成功")
        print(f"用户ID: {xianyu_live.myid}")
        print(f"设备ID: {xianyu_live.device_id}")
        print(f"WebSocket URL: {xianyu_live.base_url}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    quick_test()
    input("按回车键退出...")
