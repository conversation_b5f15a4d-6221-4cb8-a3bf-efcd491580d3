#!/usr/bin/env python3
"""
验证导入修复结果
"""

def test_critical_imports():
    """测试关键模块导入"""
    print("🔧 验证导入修复结果...")
    
    try:
        # 测试之前出错的导入
        print("1. 测试smart_reply_generator...")
        from smart_reply_generator import SmartReplyGenerator
        print("✅ smart_reply_generator导入成功")
        
        print("2. 测试smart_xianyu_agent...")
        from smart_xianyu_agent import SmartXianyuAgent
        print("✅ smart_xianyu_agent导入成功")
        
        print("3. 测试config配置...")
        from config import config
        print("✅ config配置导入成功")
        
        print("4. 测试config_gui...")
        from config_gui import ConfigGUI
        print("✅ config_gui导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def main():
    """主验证函数"""
    print("🚀 验证导入修复...")
    print("=" * 40)
    
    if test_critical_imports():
        print("\n🎉 导入问题已修复！")
        print("\n📋 现在可以正常使用：")
        print("• python main.py           # 启动选择界面")
        print("• python main.py --config  # 配置界面")
        print("• python main.py --service # 直接启动服务")
        print("• python main.py --help    # 帮助信息")
        
        print("\n✨ 修复内容：")
        print("• 修复了smart_reply_generator.py中的load_config导入错误")
        print("• 将错误的函数导入改为正确的config对象导入")
        print("• 保持了所有原有功能的完整性")
        
        return True
    else:
        print("\n❌ 仍有导入问题需要解决")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎯 集成启动功能现在可以正常使用了！")
    exit(0 if success else 1)
