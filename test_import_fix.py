#!/usr/bin/env python3
"""
测试导入修复
"""

import sys

def test_imports():
    """测试各个模块的导入"""
    print("🧪 测试模块导入...")
    
    try:
        print("1. 测试config模块...")
        from config import config
        print("✅ config模块导入成功")
        
        print("2. 测试smart_product_manager模块...")
        from smart_product_manager import SmartProductManager
        print("✅ smart_product_manager模块导入成功")
        
        print("3. 测试smart_reply_generator模块...")
        from smart_reply_generator import SmartReplyGenerator
        print("✅ smart_reply_generator模块导入成功")
        
        print("4. 测试smart_xianyu_agent模块...")
        from smart_xianyu_agent import SmartXianyuAgent
        print("✅ smart_xianyu_agent模块导入成功")
        
        print("5. 测试XianyuAgent模块...")
        from XianyuAgent import XianyuReplyBot
        print("✅ XianyuAgent模块导入成功")
        
        print("6. 测试config_gui模块...")
        from config_gui import ConfigGUI
        print("✅ config_gui模块导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_help():
    """测试main.py的帮助功能"""
    print("\n🧪 测试main.py帮助功能...")
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "main.py", "--help"], 
                              capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ main.py --help 执行成功")
            if "XianyuAutoAgent" in result.stdout:
                print("✅ 帮助信息正确显示")
                return True
            else:
                print("⚠️ 帮助信息可能不完整")
                print(f"输出: {result.stdout}")
                return False
        else:
            print(f"❌ main.py --help 执行失败，返回码: {result.returncode}")
            print(f"错误: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ main.py --help 执行超时")
        return False
    except Exception as e:
        print(f"❌ 测试main.py失败: {e}")
        return False

def test_startup_dialog():
    """测试启动对话框函数"""
    print("\n🧪 测试启动对话框函数...")
    
    try:
        # 测试tkinter可用性
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        root.destroy()
        print("✅ tkinter可用")
        
        # 测试环境变量加载
        from dotenv import load_dotenv
        import os
        load_dotenv()
        
        cookies_str = os.getenv("COOKIES_STR")
        if cookies_str:
            print("✅ Cookie配置存在")
        else:
            print("⚠️ Cookie配置不存在（这是正常的，如果还未配置）")
        
        return True
        
    except Exception as e:
        print(f"❌ 启动对话框测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试导入修复...")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("main.py帮助", test_main_help),
        ("启动对话框", test_startup_dialog),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        if test_func():
            passed += 1
        print("-" * 30)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有测试通过！导入问题已修复")
        print("\n📋 现在您可以正常使用：")
        print("1. python main.py           # 显示启动选择界面")
        print("2. python main.py --config  # 直接打开配置界面")
        print("3. python main.py --service # 直接启动服务")
        print("4. python main.py --help    # 显示帮助信息")
        return 0
    else:
        print(f"\n❌ {total - passed} 个测试失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
