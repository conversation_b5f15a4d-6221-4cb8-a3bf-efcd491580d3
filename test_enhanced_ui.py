#!/usr/bin/env python3
"""
测试增强的智能商品管理界面功能
"""

import sys
import os
from smart_product_manager import SmartProductManager, ProductInfo, ReplyStrategy

def create_test_data():
    """创建测试数据"""
    print("🔧 创建测试数据...")
    
    try:
        manager = SmartProductManager()
        
        # 创建测试商品
        test_products = [
            ProductInfo(
                item_id="test001",
                title="iPhone 15 Pro Max 256GB",
                price=8999.0,
                original_price=9999.0,
                description="全新iPhone 15 Pro Max，256GB存储，钛金属材质，支持5G网络",
                category="手机数码",
                stock_status="available"
            ),
            ProductInfo(
                item_id="test002", 
                title="MacBook Air M3 13英寸",
                price=7999.0,
                original_price=8999.0,
                description="全新MacBook Air M3芯片，13英寸视网膜显示屏，8GB内存，256GB SSD",
                category="电脑办公",
                stock_status="available"
            ),
            ProductInfo(
                item_id="test003",
                title="AirPods Pro 第三代",
                price=1899.0,
                original_price=1999.0,
                description="苹果AirPods Pro第三代，主动降噪，空间音频，无线充电盒",
                category="音频设备",
                stock_status="available"
            ),
            ProductInfo(
                item_id="test004",
                title="iPad Pro 12.9英寸 M2",
                price=6999.0,
                original_price=7999.0,
                description="iPad Pro 12.9英寸，M2芯片，128GB存储，支持Apple Pencil",
                category="平板电脑",
                stock_status="reserved"
            ),
            ProductInfo(
                item_id="test005",
                title="Apple Watch Series 9",
                price=2999.0,
                original_price=3199.0,
                description="Apple Watch Series 9，45mm表盘，GPS+蜂窝网络版，运动表带",
                category="智能穿戴",
                stock_status="sold"
            )
        ]
        
        # 保存商品
        for product in test_products:
            if manager.save_product(product):
                print(f"✓ 保存商品: {product.title}")
            else:
                print(f"✗ 保存失败: {product.title}")
        
        # 创建测试策略
        test_strategies = [
            ReplyStrategy(
                item_id="test001",
                mode="specific",
                min_price=7500.0,
                max_discount_percent=15.0,
                bargain_rounds=3,
                selling_points=["全新正品", "支持验机", "顺丰包邮"],
                auto_reply_enabled=True
            ),
            ReplyStrategy(
                item_id="test002",
                mode="general",
                min_price=7000.0,
                max_discount_percent=12.0,
                bargain_rounds=2,
                auto_reply_enabled=True
            ),
            ReplyStrategy(
                item_id="test003",
                mode="specific",
                min_price=1600.0,
                max_discount_percent=10.0,
                bargain_rounds=2,
                selling_points=["原装正品", "保修一年"],
                auto_reply_enabled=True
            )
        ]
        
        # 保存策略
        for strategy in test_strategies:
            if manager.save_reply_strategy(strategy):
                print(f"✓ 保存策略: {strategy.item_id}")
            else:
                print(f"✗ 策略保存失败: {strategy.item_id}")
        
        print(f"\n🎉 测试数据创建完成！共创建 {len(test_products)} 个商品和 {len(test_strategies)} 个策略")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_product_operations():
    """测试商品操作功能"""
    print("\n🧪 测试商品操作功能...")
    
    try:
        manager = SmartProductManager()
        
        # 测试获取所有商品
        print("1. 测试获取所有商品...")
        products = manager.get_all_products()
        print(f"✓ 获取到 {len(products)} 个商品")
        
        # 测试搜索功能
        print("2. 测试搜索功能...")
        search_results = manager.search_products("iPhone")
        print(f"✓ 搜索'iPhone'找到 {len(search_results)} 个商品")
        
        search_results = manager.search_products("test001")
        print(f"✓ 搜索'test001'找到 {len(search_results)} 个商品")
        
        # 测试删除功能
        print("3. 测试删除功能...")
        if manager.delete_product("test005"):
            print("✓ 删除商品 test005 成功")
        else:
            print("✗ 删除商品 test005 失败")
        
        # 验证删除结果
        deleted_product = manager.get_product("test005")
        if deleted_product is None:
            print("✓ 确认商品已被删除")
        else:
            print("✗ 商品删除验证失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 商品操作测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试增强的智能商品管理界面...")
    
    success = True
    
    # 创建测试数据
    if not create_test_data():
        success = False
    
    # 测试商品操作
    if not test_product_operations():
        success = False
    
    if success:
        print("\n🎉 所有测试通过！")
        print("\n📋 现在您可以：")
        print("1. 运行 'python config_gui.py' 启动配置界面")
        print("2. 切换到 '🛍️ 智能商品' 标签页")
        print("3. 测试以下新功能：")
        print("   - ✅ 多选商品（按住Ctrl点击多个商品）")
        print("   - 🗑️ 删除选中的商品")
        print("   - ⚙️ 批量设置策略")
        print("   - 🔍 搜索商品功能")
        print("   - 📝 双击编辑商品")
        return 0
    else:
        print("\n❌ 部分测试失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
