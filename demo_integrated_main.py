#!/usr/bin/env python3
"""
演示集成启动功能
"""

import tkinter as tk
from tkinter import messagebox, ttk
import sys
import os

def demo_startup_dialog():
    """演示启动选择对话框"""
    # 创建启动选择窗口
    root = tk.Tk()
    root.title("XianyuAutoAgent - 启动选择 (演示)")
    root.geometry("500x350")
    root.resizable(False, False)
    
    # 居中显示
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    result = {"action": None}
    
    # 标题
    title_frame = ttk.Frame(root)
    title_frame.pack(pady=20)
    
    title_label = ttk.Label(title_frame, text="🤖 XianyuAutoAgent", font=('Arial', 18, 'bold'))
    title_label.pack()
    
    subtitle_label = ttk.Label(title_frame, text="智能闲鱼客服助手", font=('Arial', 12))
    subtitle_label.pack(pady=(5, 0))
    
    # 分隔线
    separator = ttk.Separator(root, orient='horizontal')
    separator.pack(fill='x', padx=20, pady=10)
    
    # 选项框架
    options_frame = ttk.Frame(root)
    options_frame.pack(expand=True, fill='both', padx=40, pady=20)
    
    # 配置选项
    config_frame = ttk.LabelFrame(options_frame, text="🛠️ 配置管理", padding=15)
    config_frame.pack(fill='x', pady=(0, 15))
    
    config_desc = ttk.Label(config_frame, text="打开配置界面，设置Cookie、AI模型、商品策略等", 
                           font=('Arial', 10), foreground='gray')
    config_desc.pack(anchor='w')
    
    def open_config():
        result["action"] = "config"
        messagebox.showinfo("演示", "这里会打开配置界面\n包含所有设置选项：\n- Cookie配置\n- AI模型设置\n- 智能商品管理\n- 多选删除功能")
        root.quit()
    
    config_btn = ttk.Button(config_frame, text="🔧 打开配置界面", command=open_config, width=20)
    config_btn.pack(pady=(10, 0))
    
    # 直接启动选项
    start_frame = ttk.LabelFrame(options_frame, text="🚀 直接启动", padding=15)
    start_frame.pack(fill='x', pady=(0, 15))
    
    start_desc = ttk.Label(start_frame, text="使用当前配置直接启动智能客服服务", 
                          font=('Arial', 10), foreground='gray')
    start_desc.pack(anchor='w')
    
    def direct_start():
        result["action"] = "start"
        messagebox.showinfo("演示", "这里会启动智能客服服务\n功能包括：\n- 实时消息处理\n- 智能回复生成\n- 商品信息管理\n- 人工接管支持")
        root.quit()
    
    start_btn = ttk.Button(start_frame, text="▶️ 直接启动服务", command=direct_start, width=20)
    start_btn.pack(pady=(10, 0))
    
    # 退出选项
    exit_frame = ttk.Frame(options_frame)
    exit_frame.pack(fill='x')
    
    def exit_app():
        result["action"] = "exit"
        root.quit()
    
    exit_btn = ttk.Button(exit_frame, text="❌ 退出", command=exit_app, width=20)
    exit_btn.pack()
    
    # 状态信息
    status_frame = ttk.Frame(root)
    status_frame.pack(side='bottom', fill='x', padx=20, pady=10)
    
    # 检查配置状态（演示）
    try:
        from dotenv import load_dotenv
        load_dotenv()
        cookies_str = os.getenv("COOKIES_STR")
        if cookies_str:
            status_text = "✅ 配置文件已就绪"
            status_color = "green"
        else:
            status_text = "⚠️ 需要配置Cookie信息"
            status_color = "orange"
    except:
        status_text = "❌ 配置文件异常"
        status_color = "red"
    
    status_label = ttk.Label(status_frame, text=status_text, foreground=status_color)
    status_label.pack()
    
    # 添加演示说明
    demo_frame = ttk.Frame(root)
    demo_frame.pack(side='bottom', fill='x', padx=20, pady=(0, 10))
    
    demo_label = ttk.Label(demo_frame, text="🎭 这是演示界面，展示集成启动功能", 
                          font=('Arial', 9), foreground='blue')
    demo_label.pack()
    
    # 运行对话框
    root.mainloop()
    root.destroy()
    
    return result["action"]

def show_integration_info():
    """显示集成功能信息"""
    info_window = tk.Tk()
    info_window.title("集成功能说明")
    info_window.geometry("600x500")
    info_window.resizable(True, True)
    
    # 居中显示
    info_window.update_idletasks()
    x = (info_window.winfo_screenwidth() // 2) - (info_window.winfo_width() // 2)
    y = (info_window.winfo_screenheight() // 2) - (info_window.winfo_height() // 2)
    info_window.geometry(f"+{x}+{y}")
    
    # 创建滚动文本框
    text_frame = ttk.Frame(info_window)
    text_frame.pack(fill='both', expand=True, padx=20, pady=20)
    
    # 文本内容
    text_widget = tk.Text(text_frame, wrap='word', font=('Arial', 10))
    scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)
    
    # 布局
    text_widget.pack(side='left', fill='both', expand=True)
    scrollbar.pack(side='right', fill='y')
    
    # 插入内容
    content = """🎯 XianyuAutoAgent 集成启动功能

✨ 主要特性：
• 一键启动 - 运行 python main.py 即可
• 图形化界面 - 友好的启动选择对话框
• 配置管理 - 完整的GUI配置界面
• 智能检测 - 自动检查配置状态
• 命令行支持 - 支持快速启动参数

🚀 使用方法：

1. 基本启动：
   python main.py
   
2. 直接配置：
   python main.py --config
   
3. 直接启动服务：
   python main.py --service
   
4. 查看帮助：
   python main.py --help

🔧 配置界面功能：
• Cookie设置 - 闲鱼账号认证
• AI模型配置 - DeepSeek V3等AI服务
• 智能商品管理 - 商品策略配置
• 多选批量操作 - 批量删除、设置策略
• 通用/针对回复模式 - 灵活的回复策略

🎨 用户体验：
• 现代化UI设计
• 实时状态显示
• 错误友好提示
• 操作简单直观

📋 工作流程：
1. 首次使用：配置 → 设置 → 启动
2. 日常使用：直接启动服务
3. 调整配置：进入配置界面

🔄 集成优势：
• 统一入口 - 一个文件管理所有功能
• 简化操作 - 无需记忆复杂命令
• 状态可视 - 实时显示系统状态
• 错误处理 - 完善的异常处理机制

现在您只需要运行 python main.py 就能享受完整的智能客服体验！"""
    
    text_widget.insert('1.0', content)
    text_widget.config(state='disabled')
    
    # 关闭按钮
    close_btn = ttk.Button(info_window, text="关闭", command=info_window.destroy)
    close_btn.pack(pady=10)
    
    info_window.mainloop()

def main():
    """主演示函数"""
    print("🎭 XianyuAutoAgent 集成启动功能演示")
    print("=" * 50)
    
    # 显示集成信息
    show_integration_info()
    
    # 演示启动对话框
    print("🖥️ 演示启动选择界面...")
    action = demo_startup_dialog()
    
    if action:
        print(f"✅ 用户选择了: {action}")
    else:
        print("👋 用户关闭了对话框")
    
    print("\n🎉 演示完成！")
    print("\n📋 实际使用时，请运行：")
    print("   python main.py")

if __name__ == "__main__":
    main()
