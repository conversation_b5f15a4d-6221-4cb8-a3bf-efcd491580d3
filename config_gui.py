#!/usr/bin/env python3
"""
XianyuAutoAgent Python桌面配置界面
使用tkinter创建原生Python GUI
"""

import os
import sys
import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
from pathlib import Path
from typing import Dict, Any
import json
from dotenv import load_dotenv, set_key
from smart_product_manager import SmartProductManager, ProductInfo, ReplyStrategy

class ConfigGUI:
    """配置管理GUI"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 XianyuAutoAgent 配置管理")
        self.root.geometry("900x800")
        self.root.resizable(True, True)
        
        # 设置图标和样式
        self.setup_style()
        
        # 配置数据
        self.config_data = {}
        self.prompts_data = {}

        # 初始化智能商品管理器
        try:
            self.product_manager = SmartProductManager()
        except Exception as e:
            messagebox.showerror("错误", f"初始化智能商品管理器失败: {e}")
            self.product_manager = None

        # 创建界面
        self.create_widgets()

        # 加载配置
        self.load_config()
    
    def setup_style(self):
        """设置界面样式"""
        # 配置ttk样式
        style = ttk.Style()
        style.theme_use('clam')
        
        # 自定义样式
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'))
        style.configure('Heading.TLabel', font=('Arial', 12, 'bold'))
        style.configure('Config.TFrame', padding=10)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主标题
        title_frame = ttk.Frame(self.root)
        title_frame.pack(fill='x', padx=10, pady=10)
        
        title_label = ttk.Label(title_frame, text="🤖 XianyuAutoAgent 配置管理", style='Title.TLabel')
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="智能客服配置中心", font=('Arial', 10))
        subtitle_label.pack()

        # 创建顶部启动区域
        self.create_top_launch_area()

        # 创建笔记本控件（标签页）
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=10, pady=(5, 0))
        
        # 创建各个标签页
        self.create_basic_tab()
        self.create_ai_tab()
        self.create_prompts_tab()
        self.create_smart_product_tab()
        self.create_advanced_tab()
        
        # 底部按钮
        self.create_buttons()

    def create_top_launch_area(self):
        """创建顶部启动区域"""
        # 创建启动区域框架
        launch_frame = ttk.LabelFrame(self.root, text="🚀 服务控制", padding=15)
        launch_frame.pack(fill='x', padx=10, pady=(5, 10))

        # 创建启动按钮容器
        button_container = ttk.Frame(launch_frame)
        button_container.pack(fill='x')

        # 左侧：启动按钮
        left_frame = ttk.Frame(button_container)
        left_frame.pack(side='left', fill='x', expand=True)

        # 大号启动按钮
        self.main_start_button = ttk.Button(
            left_frame,
            text="🚀 启动智能客服服务",
            command=self.start_service,
        )
        self.main_start_button.pack(side='left', padx=(0, 20), ipadx=20, ipady=10)

        # 右侧：状态显示
        right_frame = ttk.Frame(button_container)
        right_frame.pack(side='right')

        # 状态标签
        self.main_status_label = ttk.Label(
            right_frame,
            text="● 服务未启动",
            foreground="red",
            font=('Arial', 12, 'bold')
        )
        self.main_status_label.pack(side='right', padx=(20, 0))

        # 添加分隔线
        separator = ttk.Separator(launch_frame, orient='horizontal')
        separator.pack(fill='x', pady=(10, 0))

        # 添加快速提示
        tip_label = ttk.Label(
            launch_frame,
            text="💡 提示：请先完成基础配置（API密钥、Cookie等），然后点击启动按钮开始服务",
            font=('Arial', 9),
            foreground="gray"
        )
        tip_label.pack(pady=(5, 0))

    def update_service_status(self, text, color):
        """更新服务状态（同时更新顶部和底部状态标签）"""
        # 更新顶部状态标签
        if hasattr(self, 'main_status_label'):
            status_text = f"● {text}"
            self.main_status_label.config(text=status_text, foreground=color)

        # 更新底部状态标签（如果存在）
        if hasattr(self, 'start_status_label'):
            self.start_status_label.config(text=text, foreground=color)

    def create_basic_tab(self):
        """创建基础配置标签页"""
        frame = ttk.Frame(self.notebook, style='Config.TFrame')
        self.notebook.add(frame, text="🔑 基础配置")
        
        # 滚动框架
        canvas = tk.Canvas(frame)
        scrollbar = ttk.Scrollbar(frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # API配置
        api_group = ttk.LabelFrame(scrollable_frame, text="🔐 API配置", padding=10)
        api_group.pack(fill='x', pady=5)
        
        ttk.Label(api_group, text="OpenAI API密钥 *:").pack(anchor='w')
        self.api_key_var = tk.StringVar()
        api_entry = ttk.Entry(api_group, textvariable=self.api_key_var, show="*", width=60)
        api_entry.pack(fill='x', pady=2)
        
        ttk.Label(api_group, text="API基础URL:").pack(anchor='w', pady=(10,0))
        self.base_url_var = tk.StringVar(value="https://api.openai.com/v1")

        # API URL配置框架
        url_frame = ttk.Frame(api_group)
        url_frame.pack(fill='x', pady=2)

        ttk.Entry(url_frame, textvariable=self.base_url_var, width=40).pack(side='left', fill='x', expand=True)
        ttk.Button(url_frame, text="🤖 DeepSeek",
                  command=self.set_deepseek_config).pack(side='right', padx=(5,0))
        ttk.Button(url_frame, text="🔄 OpenAI",
                  command=self.set_openai_config).pack(side='right', padx=(5,0))

        # 模型选择
        ttk.Label(api_group, text="AI模型:").pack(anchor='w', pady=(10,0))
        self.model_var = tk.StringVar(value="gpt-3.5-turbo")
        model_frame = ttk.Frame(api_group)
        model_frame.pack(fill='x', pady=2)

        model_combo = ttk.Combobox(model_frame, textvariable=self.model_var, width=30, state="readonly")
        model_combo['values'] = (
            'gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo', 'gpt-4o',
            'deepseek-chat', 'deepseek-reasoner'
        )
        model_combo.pack(side='left', fill='x', expand=True)

        ttk.Button(model_frame, text="💡 推荐",
                  command=self.show_model_recommendations).pack(side='right', padx=(5,0))
        
        # 闲鱼配置
        xianyu_group = ttk.LabelFrame(scrollable_frame, text="🐟 闲鱼配置", padding=10)
        xianyu_group.pack(fill='x', pady=5)

        ttk.Label(xianyu_group, text="Cookie字符串 * (必须包含unb字段):").pack(anchor='w')
        self.cookies_var = tk.StringVar()
        cookies_text = scrolledtext.ScrolledText(xianyu_group, height=4, width=70)
        cookies_text.pack(fill='x', pady=2)
        self.cookies_text = cookies_text

        # Cookie操作按钮
        cookie_buttons = ttk.Frame(xianyu_group)
        cookie_buttons.pack(fill='x', pady=5)

        ttk.Button(cookie_buttons, text="🔍 验证Cookie",
                  command=self.validate_cookie).pack(side='left', padx=5)
        ttk.Button(cookie_buttons, text="📖 获取教程",
                  command=self.show_cookie_guide).pack(side='left', padx=5)

        # Cookie状态显示
        self.cookie_status_var = tk.StringVar(value="未验证")
        self.cookie_status_label = ttk.Label(xianyu_group, textvariable=self.cookie_status_var)
        self.cookie_status_label.pack(anchor='w', pady=2)
        
        # 安全配置
        security_group = ttk.LabelFrame(scrollable_frame, text="🔒 安全配置", padding=10)
        security_group.pack(fill='x', pady=5)
        
        ttk.Label(security_group, text="加密密钥 (可选):").pack(anchor='w')
        self.encryption_key_var = tk.StringVar()
        ttk.Entry(security_group, textvariable=self.encryption_key_var, show="*", width=60).pack(fill='x', pady=2)
        
        # 生成密钥按钮
        ttk.Button(security_group, text="🔑 生成随机密钥", 
                  command=self.generate_encryption_key).pack(anchor='w', pady=5)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_ai_tab(self):
        """创建AI设置标签页"""
        frame = ttk.Frame(self.notebook, style='Config.TFrame')
        self.notebook.add(frame, text="🧠 AI设置")
        
        # AI模型配置
        model_group = ttk.LabelFrame(frame, text="🤖 模型配置", padding=10)
        model_group.pack(fill='x', pady=5)
        
        ttk.Label(model_group, text="AI模型:").pack(anchor='w')
        self.model_var = tk.StringVar(value="gpt-4o-mini")
        model_combo = ttk.Combobox(model_group, textvariable=self.model_var, 
                                  values=["gpt-4o-mini", "gpt-4o", "gpt-3.5-turbo"], 
                                  state="readonly", width=30)
        model_combo.pack(anchor='w', pady=2)
        
        # 参数配置
        params_group = ttk.LabelFrame(frame, text="⚙️ 参数配置", padding=10)
        params_group.pack(fill='x', pady=5)
        
        # 温度设置
        temp_frame = ttk.Frame(params_group)
        temp_frame.pack(fill='x', pady=2)
        ttk.Label(temp_frame, text="创造性温度 (0-2):").pack(side='left')
        self.temperature_var = tk.DoubleVar(value=0.7)
        temp_scale = ttk.Scale(temp_frame, from_=0, to=2, variable=self.temperature_var, 
                              orient='horizontal', length=200)
        temp_scale.pack(side='left', padx=10)
        self.temp_label = ttk.Label(temp_frame, text="0.7")
        self.temp_label.pack(side='left')
        temp_scale.configure(command=self.update_temp_label)
        
        # 最大令牌数
        tokens_frame = ttk.Frame(params_group)
        tokens_frame.pack(fill='x', pady=2)
        ttk.Label(tokens_frame, text="最大令牌数:").pack(side='left')
        self.max_tokens_var = tk.IntVar(value=2000)
        tokens_spin = ttk.Spinbox(tokens_frame, from_=100, to=8000, 
                                 textvariable=self.max_tokens_var, width=10)
        tokens_spin.pack(side='left', padx=10)
    
    def create_prompts_tab(self):
        """创建提示词标签页"""
        frame = ttk.Frame(self.notebook, style='Config.TFrame')
        self.notebook.add(frame, text="📝 提示词")
        
        # 提示词选择
        prompt_select_frame = ttk.Frame(frame)
        prompt_select_frame.pack(fill='x', pady=5)
        
        ttk.Label(prompt_select_frame, text="选择提示词类型:").pack(side='left')
        self.prompt_type_var = tk.StringVar(value="classify_prompt")
        prompt_combo = ttk.Combobox(prompt_select_frame, textvariable=self.prompt_type_var,
                                   values=["classify_prompt", "price_prompt", "tech_prompt", "default_prompt"],
                                   state="readonly", width=20)
        prompt_combo.pack(side='left', padx=10)
        prompt_combo.bind('<<ComboboxSelected>>', self.on_prompt_type_change)
        
        # 提示词编辑区
        self.prompt_text = scrolledtext.ScrolledText(frame, height=25, width=80)
        self.prompt_text.pack(fill='both', expand=True, pady=5)
        
        # 提示词说明
        info_frame = ttk.LabelFrame(frame, text="📖 提示词说明", padding=5)
        info_frame.pack(fill='x', pady=5)
        
        self.prompt_info_label = ttk.Label(info_frame, text="", wraplength=600)
        self.prompt_info_label.pack()
        
        self.update_prompt_info()
    
    def create_advanced_tab(self):
        """创建高级设置标签页"""
        frame = ttk.Frame(self.notebook, style='Config.TFrame')
        self.notebook.add(frame, text="⚙️ 高级设置")
        
        # WebSocket配置
        ws_group = ttk.LabelFrame(frame, text="🌐 WebSocket配置", padding=10)
        ws_group.pack(fill='x', pady=5)
        
        ttk.Label(ws_group, text="WebSocket URL:").pack(anchor='w')
        self.ws_url_var = tk.StringVar(value="wss://h5api.m.taobao.com/h5/mtop.taobao.idle.im.ws.connect/1.0/")
        ttk.Entry(ws_group, textvariable=self.ws_url_var, width=70).pack(fill='x', pady=2)
        
        ttk.Label(ws_group, text="超时时间(秒):").pack(anchor='w', pady=(10,0))
        self.ws_timeout_var = tk.IntVar(value=30)
        ttk.Spinbox(ws_group, from_=10, to=300, textvariable=self.ws_timeout_var, width=10).pack(anchor='w', pady=2)
        
        # 数据库配置
        db_group = ttk.LabelFrame(frame, text="🗄️ 数据库配置", padding=10)
        db_group.pack(fill='x', pady=5)
        
        db_frame = ttk.Frame(db_group)
        db_frame.pack(fill='x')
        ttk.Label(db_frame, text="数据库路径:").pack(side='left')
        self.db_path_var = tk.StringVar(value="data/chat_history.db")
        ttk.Entry(db_frame, textvariable=self.db_path_var, width=40).pack(side='left', padx=5)
        ttk.Button(db_frame, text="浏览", command=self.browse_db_path).pack(side='left')
        
        # 日志配置
        log_group = ttk.LabelFrame(frame, text="📊 日志配置", padding=10)
        log_group.pack(fill='x', pady=5)
        
        log_level_frame = ttk.Frame(log_group)
        log_level_frame.pack(fill='x', pady=2)
        ttk.Label(log_level_frame, text="日志级别:").pack(side='left')
        self.log_level_var = tk.StringVar(value="INFO")
        ttk.Combobox(log_level_frame, textvariable=self.log_level_var,
                    values=["DEBUG", "INFO", "WARNING", "ERROR"], 
                    state="readonly", width=15).pack(side='left', padx=10)
        
        ttk.Label(log_group, text="日志文件路径:").pack(anchor='w', pady=(10,0))
        self.log_path_var = tk.StringVar(value="logs/xianyu_agent.log")
        ttk.Entry(log_group, textvariable=self.log_path_var, width=50).pack(fill='x', pady=2)
    
    def create_buttons(self):
        """创建底部按钮"""
        # 创建分隔线
        separator = ttk.Separator(self.root, orient='horizontal')
        separator.pack(fill='x', padx=10, pady=(5, 0))

        button_frame = ttk.Frame(self.root, style='Config.TFrame')
        button_frame.pack(fill='x', padx=10, pady=10, side='bottom')

        # 左侧按钮
        left_buttons = ttk.Frame(button_frame)
        left_buttons.pack(side='left')

        ttk.Button(left_buttons, text="🔄 重新加载", command=self.load_config).pack(side='left', padx=5)
        ttk.Button(left_buttons, text="📁 打开配置目录", command=self.open_config_dir).pack(side='left', padx=5)

        # 中间按钮 - 启动服务
        center_buttons = ttk.Frame(button_frame)
        center_buttons.pack(side='left', expand=True, fill='x', padx=20)

        # 创建启动按钮，居中显示，使用更大的样式
        start_button = ttk.Button(center_buttons, text="🚀 启动服务", command=self.start_service)
        start_button.pack(anchor='center', pady=5, ipadx=15, ipady=8)

        # 添加启动状态标签
        self.start_status_label = ttk.Label(center_buttons, text="点击启动按钮开始服务", foreground="blue", font=('Arial', 10, 'bold'))
        self.start_status_label.pack(anchor='center')

        # 右侧按钮
        right_buttons = ttk.Frame(button_frame)
        right_buttons.pack(side='right')

        ttk.Button(right_buttons, text="🧪 测试配置", command=self.test_config).pack(side='right', padx=5)
        ttk.Button(right_buttons, text="💾 保存配置", command=self.save_config).pack(side='right', padx=5)
    
    def update_temp_label(self, value):
        """更新温度标签"""
        self.temp_label.config(text=f"{float(value):.1f}")
    
    def on_prompt_type_change(self, event=None):
        """提示词类型改变时的处理"""
        self.save_current_prompt()
        self.load_current_prompt()
        self.update_prompt_info()
    
    def update_prompt_info(self):
        """更新提示词说明"""
        info_texts = {
            "classify_prompt": "用于判断用户消息类型（价格、技术、一般咨询）的提示词",
            "price_prompt": "处理价格相关咨询（询价、议价、优惠等）的提示词",
            "tech_prompt": "处理技术问题（功能、参数、使用方法等）的提示词",
            "default_prompt": "处理一般咨询（发货、售后、退换货等）的提示词"
        }
        
        current_type = self.prompt_type_var.get()
        info_text = info_texts.get(current_type, "")
        self.prompt_info_label.config(text=info_text)
    
    def generate_encryption_key(self):
        """生成随机加密密钥"""
        import secrets
        key = secrets.token_urlsafe(32)
        self.encryption_key_var.set(key)
        messagebox.showinfo("密钥生成", "已生成新的加密密钥！")
    
    def browse_db_path(self):
        """浏览数据库路径"""
        filename = filedialog.asksaveasfilename(
            title="选择数据库文件",
            defaultextension=".db",
            filetypes=[("SQLite数据库", "*.db"), ("所有文件", "*.*")]
        )
        if filename:
            self.db_path_var.set(filename)
    
    def open_config_dir(self):
        """打开配置目录"""
        import subprocess
        import platform
        
        config_dir = Path.cwd()
        
        if platform.system() == "Windows":
            subprocess.run(["explorer", str(config_dir)])
        elif platform.system() == "Darwin":  # macOS
            subprocess.run(["open", str(config_dir)])
        else:  # Linux
            subprocess.run(["xdg-open", str(config_dir)])
    
    def load_config(self):
        """加载配置 - 使用统一的环境变量名称"""
        try:
            # 确保目录存在
            Path("prompts").mkdir(exist_ok=True)
            Path("data").mkdir(exist_ok=True)
            Path("logs").mkdir(exist_ok=True)

            # 统一的环境变量获取函数 (支持别名映射)
            def get_unified_env(primary_key: str, aliases: list = None, default: str = "") -> str:
                """获取环境变量，支持别名映射"""
                if aliases is None:
                    aliases = []

                # 首先尝试主键
                value = os.getenv(primary_key)
                if value is not None:
                    return value

                # 然后尝试别名
                for alias in aliases:
                    value = os.getenv(alias)
                    if value is not None:
                        return value

                return default

            # 只在第一次加载时调用load_dotenv
            if not hasattr(self, '_config_loaded'):
                load_dotenv()
                self._config_loaded = True

            # 加载基础配置 (使用统一的环境变量名称)
            self.api_key_var.set(get_unified_env("API_KEY", default=""))
            self.base_url_var.set(get_unified_env("BASE_URL", default="https://api.deepseek.com/v1"))
            self.cookies_text.delete(1.0, tk.END)
            self.cookies_text.insert(1.0, get_unified_env("COOKIES_STR", default=""))
            self.encryption_key_var.set(get_unified_env("ENCRYPTION_KEY", default=""))

            # 加载AI配置 (使用统一的默认值)
            self.model_var.set(get_unified_env("MODEL", ["MODEL_NAME"], "deepseek-chat"))
            self.temperature_var.set(float(get_unified_env("TEMPERATURE", default="0.7")))
            self.max_tokens_var.set(int(get_unified_env("MAX_TOKENS", default="2000")))

            # 加载高级配置 (解决命名冲突)
            self.ws_url_var.set(get_unified_env("WS_BASE_URL", ["WEBSOCKET_URL"], "wss://wss-goofish.dingtalk.com/"))
            self.ws_timeout_var.set(int(get_unified_env("WS_TIMEOUT", ["HEARTBEAT_TIMEOUT"], "30")))
            self.db_path_var.set(get_unified_env("DATABASE_PATH", ["DB_PATH"], "data/chat_history.db"))
            self.log_level_var.set(get_unified_env("LOG_LEVEL", default="INFO"))
            self.log_path_var.set(get_unified_env("LOG_FILE_PATH", default="logs/xianyu_agent.log"))
            
            # 加载提示词
            self.load_prompts()
            self.load_current_prompt()
            
            messagebox.showinfo("成功", "配置加载成功！")
            
        except Exception as e:
            messagebox.showerror("错误", f"配置加载失败：{str(e)}")
    
    def load_prompts(self):
        """加载所有提示词"""
        prompt_files = {
            "classify_prompt": "classify_prompt.txt",
            "price_prompt": "price_prompt.txt",
            "tech_prompt": "tech_prompt.txt",
            "default_prompt": "default_prompt.txt"
        }
        
        self.prompts_data = {}
        
        for key, filename in prompt_files.items():
            file_path = Path("prompts") / filename
            try:
                if file_path.exists():
                    self.prompts_data[key] = file_path.read_text(encoding='utf-8')
                else:
                    self.prompts_data[key] = ""
            except Exception as e:
                print(f"加载提示词文件失败 {filename}: {e}")
                self.prompts_data[key] = ""
    
    def load_current_prompt(self):
        """加载当前选中的提示词"""
        current_type = self.prompt_type_var.get()
        prompt_content = self.prompts_data.get(current_type, "")
        
        self.prompt_text.delete(1.0, tk.END)
        self.prompt_text.insert(1.0, prompt_content)
    
    def save_current_prompt(self):
        """保存当前编辑的提示词"""
        current_type = self.prompt_type_var.get()
        prompt_content = self.prompt_text.get(1.0, tk.END).strip()
        self.prompts_data[current_type] = prompt_content
    
    def save_config(self):
        """保存配置"""
        try:
            # 验证必需字段
            if not self.api_key_var.get().strip():
                messagebox.showerror("错误", "API密钥不能为空！")
                return

            cookie_str = self.cookies_text.get(1.0, tk.END).strip()
            if not cookie_str:
                messagebox.showerror("错误", "Cookie字符串不能为空！")
                return

            # 验证Cookie必要字段
            required_fields = ['unb', '_tb_token_', 'cookie2']
            missing_fields = []

            for field in required_fields:
                if field not in cookie_str:
                    missing_fields.append(field)

            if missing_fields:
                result = messagebox.askyesno("Cookie警告",
                    f"Cookie中缺少必要字段：{', '.join(missing_fields)}\n\n"
                    "这可能导致程序无法正常运行。\n"
                    "是否仍要保存配置？\n\n"
                    "建议点击'否'，然后使用'📖 获取教程'重新获取Cookie。")
                if not result:
                    return
            
            # 确保.env文件存在
            env_file = Path(".env")
            if not env_file.exists():
                env_file.touch()
            
            # 保存环境配置 (使用统一的环境变量名称)
            config_items = [
                ("API_KEY", self.api_key_var.get()),
                ("BASE_URL", self.base_url_var.get()),
                ("MODEL", self.model_var.get()),
                ("TEMPERATURE", str(self.temperature_var.get())),
                ("MAX_TOKENS", str(self.max_tokens_var.get())),
                ("COOKIES_STR", cookie_str),
                ("ENCRYPTION_KEY", self.encryption_key_var.get()),
                ("WS_BASE_URL", self.ws_url_var.get()),  # 统一使用WS_BASE_URL
                ("WS_TIMEOUT", str(self.ws_timeout_var.get())),  # 统一使用WS_TIMEOUT
                ("DATABASE_PATH", self.db_path_var.get()),  # 统一使用DATABASE_PATH
                ("LOG_LEVEL", self.log_level_var.get()),
                ("LOG_FILE_PATH", self.log_path_var.get()),
                ("TOP_P", "0.8"),  # 添加AI Top-P配置
            ]
            
            for key, value in config_items:
                set_key(".env", key, value)
            
            # 保存当前编辑的提示词
            self.save_current_prompt()
            
            # 保存所有提示词文件
            prompt_files = {
                "classify_prompt": "classify_prompt.txt",
                "price_prompt": "price_prompt.txt",
                "tech_prompt": "tech_prompt.txt",
                "default_prompt": "default_prompt.txt"
            }
            
            for key, filename in prompt_files.items():
                file_path = Path("prompts") / filename
                content = self.prompts_data.get(key, "")
                if content:
                    file_path.write_text(content, encoding='utf-8')
            
            messagebox.showinfo("成功", "配置保存成功！")
            
        except Exception as e:
            messagebox.showerror("错误", f"配置保存失败：{str(e)}")
    
    def validate_cookie(self):
        """验证Cookie"""
        try:
            cookie_str = self.cookies_text.get(1.0, tk.END).strip()
            if not cookie_str:
                self.cookie_status_var.set("❌ Cookie为空")
                messagebox.showerror("验证失败", "Cookie字符串不能为空！")
                return

            # 检查必要字段
            required_fields = ['unb', '_tb_token_', 'cookie2']
            missing_fields = []

            for field in required_fields:
                if field not in cookie_str:
                    missing_fields.append(field)

            if missing_fields:
                self.cookie_status_var.set(f"❌ 缺少字段: {', '.join(missing_fields)}")
                messagebox.showerror("验证失败",
                    f"Cookie中缺少必要字段：{', '.join(missing_fields)}\n\n"
                    "请确保从闲鱼网站完整复制Cookie字符串。\n"
                    "点击'📖 获取教程'查看详细获取方法。")
                return

            # 检查unb字段格式
            import re
            unb_match = re.search(r'unb=([^;]+)', cookie_str)
            if unb_match:
                unb_value = unb_match.group(1)
                if len(unb_value) < 10:
                    self.cookie_status_var.set("❌ unb字段格式异常")
                    messagebox.showerror("验证失败", "unb字段格式异常，请重新获取Cookie")
                    return

            self.cookie_status_var.set("✅ Cookie验证通过")
            messagebox.showinfo("验证成功", "Cookie验证通过！包含所有必要字段。")

        except Exception as e:
            self.cookie_status_var.set("❌ 验证出错")
            messagebox.showerror("验证失败", f"Cookie验证失败：{str(e)}")

    def set_deepseek_config(self):
        """设置DeepSeek API配置"""
        self.base_url_var.set("https://api.deepseek.com/v1")
        self.model_var.set("deepseek-chat")
        messagebox.showinfo("配置更新", "已切换到DeepSeek API配置\n请确保填入有效的DeepSeek API密钥")

    def set_openai_config(self):
        """设置OpenAI API配置"""
        self.base_url_var.set("https://api.openai.com/v1")
        self.model_var.set("gpt-3.5-turbo")
        messagebox.showinfo("配置更新", "已切换到OpenAI API配置\n请确保填入有效的OpenAI API密钥")

    def show_model_recommendations(self):
        """显示模型推荐信息"""
        recommendations = """🤖 AI模型推荐指南

📊 性能对比:
• DeepSeek-V3 (deepseek-chat): 🌟🌟🌟🌟🌟
  - 最新模型，性能优异
  - 中文理解能力强
  - 成本相对较低

• DeepSeek-R1 (deepseek-reasoner): 🧠🧠🧠🧠🧠
  - 推理能力强
  - 适合复杂问题
  - 响应时间较长

• GPT-4o: 🌟🌟🌟🌟
  - OpenAI最新模型
  - 多模态支持
  - 成本较高

• GPT-3.5-turbo: 🌟🌟🌟
  - 经典模型
  - 响应速度快
  - 成本较低

💡 推荐配置:
• 日常客服: deepseek-chat
• 复杂咨询: deepseek-reasoner
• 预算有限: gpt-3.5-turbo"""

        # 创建推荐窗口
        rec_window = tk.Toplevel(self.root)
        rec_window.title("🤖 AI模型推荐")
        rec_window.geometry("500x600")
        rec_window.resizable(False, False)

        # 使窗口居中
        rec_window.transient(self.root)
        rec_window.grab_set()

        # 创建滚动文本框
        text_frame = ttk.Frame(rec_window)
        text_frame.pack(fill='both', expand=True, padx=10, pady=10)

        text_widget = tk.Text(text_frame, wrap='word', font=('Microsoft YaHei', 10))
        scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        text_widget.insert('1.0', recommendations)
        text_widget.config(state='disabled')

        # 关闭按钮
        ttk.Button(rec_window, text="关闭", command=rec_window.destroy).pack(pady=10)

    def show_cookie_guide(self):
        """显示Cookie获取教程"""
        guide_window = tk.Toplevel(self.root)
        guide_window.title("🐟 闲鱼Cookie获取教程")
        guide_window.geometry("600x500")
        guide_window.resizable(True, True)

        # 创建滚动文本框
        text_frame = ttk.Frame(guide_window)
        text_frame.pack(fill='both', expand=True, padx=10, pady=10)

        guide_text = scrolledtext.ScrolledText(text_frame, wrap=tk.WORD, width=70, height=25)
        guide_text.pack(fill='both', expand=True)

        # 教程内容
        guide_content = """🐟 闲鱼Cookie获取详细教程

📋 步骤说明：

1. 🌐 打开浏览器
   - 推荐使用Chrome、Edge或Firefox浏览器
   - 确保浏览器是最新版本

2. 🔐 登录闲鱼
   - 访问：https://www.xianyu.com
   - 使用您的淘宝/支付宝账号登录
   - 确保登录状态正常

3. 🛠️ 打开开发者工具
   - 按F12键，或右键选择"检查"
   - 切换到"Network"（网络）标签页
   - 如果是中文界面，选择"网络"标签

4. 🔄 触发网络请求
   - 在闲鱼页面进行任何操作（如刷新页面、点击商品等）
   - 在Network标签中会出现很多请求

5. 📋 复制Cookie
   - 点击任意一个请求（推荐选择xianyu.com域名的请求）
   - 在右侧面板找到"Request Headers"（请求标头）
   - 找到"Cookie:"行，复制完整的Cookie值
   - Cookie通常很长，包含多个字段，用分号分隔

6. ✅ 验证Cookie
   - 将复制的Cookie粘贴到配置界面
   - 点击"🔍 验证Cookie"按钮
   - 确保包含以下必要字段：
     * unb: 用户标识
     * _tb_token_: 认证令牌
     * cookie2: 会话信息

⚠️ 重要提示：

• Cookie包含您的登录信息，请妥善保管
• Cookie有时效性，如果失效需要重新获取
• 不要在公共场所或不安全的网络环境中操作
• 建议定期更新Cookie以保持有效性

🔍 常见问题：

Q: Cookie验证失败怎么办？
A: 1. 确认已正确登录闲鱼
   2. 重新复制完整的Cookie字符串
   3. 检查是否包含所有必要字段

Q: unb字段是什么？
A: unb是用户的唯一标识符，格式通常是数字，用于身份验证

Q: Cookie多久需要更新？
A: 通常几天到几周，如果程序提示Cookie失效就需要更新

📞 如需帮助，请检查：
1. 浏览器控制台是否有错误信息
2. 网络连接是否正常
3. 闲鱼账号是否正常登录
"""

        guide_text.insert(1.0, guide_content)
        guide_text.config(state='disabled')  # 设为只读

        # 关闭按钮
        ttk.Button(guide_window, text="关闭", command=guide_window.destroy).pack(pady=10)

    def test_config(self):
        """测试配置"""
        try:
            # 验证API密钥
            if not self.api_key_var.get().strip():
                messagebox.showerror("测试失败", "API密钥不能为空！")
                return

            # 验证Cookie
            cookie_str = self.cookies_text.get(1.0, tk.END).strip()
            if not cookie_str:
                messagebox.showerror("测试失败", "Cookie字符串不能为空！")
                return

            # 检查Cookie必要字段
            required_fields = ['unb', '_tb_token_', 'cookie2']
            missing_fields = []

            for field in required_fields:
                if field not in cookie_str:
                    missing_fields.append(field)

            if missing_fields:
                messagebox.showerror("测试失败",
                    f"Cookie中缺少必要字段：{', '.join(missing_fields)}\n\n"
                    "请点击'📖 获取教程'查看如何正确获取Cookie。")
                return

            # 验证温度值
            temp = self.temperature_var.get()
            if temp < 0 or temp > 2:
                messagebox.showerror("测试失败", "温度值必须在0-2之间！")
                return

            # 验证令牌数
            tokens = self.max_tokens_var.get()
            if tokens < 1 or tokens > 8000:
                messagebox.showerror("测试失败", "最大令牌数必须在1-8000之间！")
                return

            # 验证提示词
            self.save_current_prompt()
            empty_prompts = []
            for key, content in self.prompts_data.items():
                if not content.strip():
                    empty_prompts.append(key)

            if empty_prompts:
                messagebox.showwarning("配置警告",
                    f"以下提示词为空：{', '.join(empty_prompts)}\n\n"
                    "建议填写提示词以获得更好的AI回复效果。")

            messagebox.showinfo("测试成功",
                "✅ 配置验证通过！\n\n"
                "• API密钥格式正确\n"
                "• Cookie包含必要字段\n"
                "• AI参数设置合理\n"
                "• 提示词配置完整\n\n"
                "现在可以运行主程序了！")

        except Exception as e:
            messagebox.showerror("测试失败", f"配置测试失败：{str(e)}")

    def start_service(self):
        """启动智能客服服务"""
        try:
            # 更新状态标签（同时更新顶部和底部的状态）
            self.update_service_status("正在检查配置...", "orange")
            self.root.update()

            # 先保存当前配置
            if not self.save_config():
                self.update_service_status("配置保存失败", "red")
                return

            # 验证关键配置
            api_key = self.api_key_var.get().strip()
            cookie_str = self.cookies_text.get(1.0, tk.END).strip()

            if not api_key:
                messagebox.showerror("启动失败", "请先配置API密钥！")
                self.update_service_status("缺少API密钥", "red")
                return

            if not cookie_str:
                messagebox.showerror("启动失败", "请先配置Cookie字符串！")
                self.update_service_status("缺少Cookie配置", "red")
                return

            # 检查Cookie必要字段
            required_fields = ['unb', '_tb_token_', 'cookie2']
            missing_fields = [field for field in required_fields if field not in cookie_str]

            if missing_fields:
                messagebox.showerror("启动失败",
                    f"Cookie中缺少必要字段：{', '.join(missing_fields)}\n\n"
                    "请点击'📖 获取教程'查看如何正确获取Cookie。")
                self.update_service_status("Cookie配置不完整", "red")
                return

            # 更新状态
            self.update_service_status("正在启动服务...", "blue")
            self.root.update()

            # 询问用户是否要关闭配置界面
            result = messagebox.askyesnocancel("启动服务",
                "配置验证通过！即将启动智能客服服务。\n\n"
                "是否关闭配置界面？\n\n"
                "• 是：关闭配置界面并启动服务\n"
                "• 否：保持配置界面打开并启动服务\n"
                "• 取消：不启动服务")

            if result is None:  # 取消
                self.update_service_status("启动已取消", "gray")
                return

            # 导入并启动服务
            try:
                import subprocess
                import sys
                import os

                # 启动服务进程
                self.update_service_status("正在启动...", "blue")
                self.root.update()

                # 使用subprocess启动服务
                process = subprocess.Popen([
                    sys.executable, "main.py", "--service"
                ], cwd=os.getcwd(), creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)

                # 更新状态
                self.update_service_status("服务已启动", "green")

                # 显示成功消息
                messagebox.showinfo("启动成功",
                    "🎉 智能客服服务已成功启动！\n\n"
                    "服务正在新的控制台窗口中运行。\n"
                    "您可以查看该窗口了解服务运行状态。")

                # 如果用户选择关闭配置界面
                if result:  # 是
                    self.root.after(1000, self.root.destroy)  # 1秒后关闭

            except Exception as e:
                self.update_service_status("启动失败", "red")
                messagebox.showerror("启动失败", f"无法启动服务：{str(e)}")

        except Exception as e:
            self.update_service_status("启动错误", "red")
            messagebox.showerror("启动错误", f"启动过程中发生错误：{str(e)}")

    def run(self):
        """运行GUI"""
        self.root.mainloop()

    def create_smart_product_tab(self):
        """创建智能商品管理标签页"""
        if not self.product_manager:
            return

        frame = ttk.Frame(self.notebook, style='Config.TFrame')
        self.notebook.add(frame, text="🛍️ 智能商品")

        # 创建主要区域
        main_frame = ttk.Frame(frame)
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # 模式选择区域
        mode_frame = ttk.LabelFrame(main_frame, text="回复模式设置", padding=10)
        mode_frame.pack(fill='x', pady=(0, 10))

        self.reply_mode = tk.StringVar(value="general")

        ttk.Radiobutton(mode_frame, text="🤖 通用模式 (AI自动分析商品信息)",
                       variable=self.reply_mode, value="general",
                       command=self.on_mode_change).pack(anchor='w', pady=2)

        ttk.Radiobutton(mode_frame, text="🎯 针对性模式 (自定义商品回复策略)",
                       variable=self.reply_mode, value="specific",
                       command=self.on_mode_change).pack(anchor='w', pady=2)

        # 商品管理区域
        product_frame = ttk.LabelFrame(main_frame, text="商品信息管理", padding=10)
        product_frame.pack(fill='both', expand=True, pady=(0, 10))

        # 商品搜索
        search_frame = ttk.Frame(product_frame)
        search_frame.pack(fill='x', pady=(0, 10))

        ttk.Label(search_frame, text="商品ID:").pack(side='left')
        self.product_id_var = tk.StringVar()
        ttk.Entry(search_frame, textvariable=self.product_id_var, width=20).pack(side='left', padx=(5, 10))

        ttk.Button(search_frame, text="🔍 搜索", command=self.search_product).pack(side='left', padx=2)
        ttk.Button(search_frame, text="➕ 添加", command=self.add_product).pack(side='left', padx=2)
        ttk.Button(search_frame, text="🔄 刷新", command=self.refresh_products).pack(side='left', padx=2)

        # 批量操作按钮
        batch_frame = ttk.Frame(product_frame)
        batch_frame.pack(fill='x', pady=5)

        ttk.Button(batch_frame, text="✅ 全选", command=self.select_all_products).pack(side='left', padx=2)
        ttk.Button(batch_frame, text="❌ 取消全选", command=self.deselect_all_products).pack(side='left', padx=2)
        ttk.Button(batch_frame, text="🗑️ 删除选中", command=self.delete_selected_products).pack(side='left', padx=2)
        ttk.Button(batch_frame, text="⚙️ 批量设置", command=self.batch_set_strategy).pack(side='left', padx=2)

        # 选中数量显示
        self.selected_count_var = tk.StringVar(value="已选中: 0 个商品")
        ttk.Label(batch_frame, textvariable=self.selected_count_var, foreground='blue').pack(side='right', padx=10)

        # 商品列表
        list_frame = ttk.Frame(product_frame)
        list_frame.pack(fill='both', expand=True)

        # 创建Treeview (支持多选)
        columns = ('ID', '标题', '价格', '状态', '模式')
        self.product_tree = ttk.Treeview(list_frame, columns=columns, show='headings', height=8, selectmode='extended')

        # 设置列标题
        for col in columns:
            self.product_tree.heading(col, text=col)
            self.product_tree.column(col, width=100)

        # 滚动条
        tree_scroll = ttk.Scrollbar(list_frame, orient='vertical', command=self.product_tree.yview)
        self.product_tree.configure(yscrollcommand=tree_scroll.set)

        self.product_tree.pack(side='left', fill='both', expand=True)
        tree_scroll.pack(side='right', fill='y')

        # 绑定选择事件
        self.product_tree.bind('<<TreeviewSelect>>', self.on_product_select)
        self.product_tree.bind('<Double-1>', self.on_product_double_click)

        # 商品详情和策略配置区域
        detail_frame = ttk.LabelFrame(main_frame, text="商品详情与策略配置", padding=10)
        detail_frame.pack(fill='x')

        # 创建详情区域的notebook
        self.detail_notebook = ttk.Notebook(detail_frame)
        self.detail_notebook.pack(fill='both', expand=True)

        # 商品信息标签页
        info_frame = ttk.Frame(self.detail_notebook)
        self.detail_notebook.add(info_frame, text="📋 商品信息")

        # 回复策略标签页
        strategy_frame = ttk.Frame(self.detail_notebook)
        self.detail_notebook.add(strategy_frame, text="⚙️ 回复策略")

        self.create_product_info_tab(info_frame)
        self.create_strategy_config_tab(strategy_frame)

        # 初始加载商品列表
        self.refresh_products()

    def create_product_info_tab(self, parent):
        """创建商品信息标签页"""
        # 商品基本信息
        basic_frame = ttk.LabelFrame(parent, text="基本信息", padding=10)
        basic_frame.pack(fill='x', pady=(0, 10))

        # 标题
        ttk.Label(basic_frame, text="商品标题:").grid(row=0, column=0, sticky='w', pady=2)
        self.product_title_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.product_title_var, width=50).grid(row=0, column=1, sticky='ew', padx=(5, 0))

        # 价格
        ttk.Label(basic_frame, text="当前价格:").grid(row=1, column=0, sticky='w', pady=2)
        self.product_price_var = tk.StringVar()
        ttk.Entry(basic_frame, textvariable=self.product_price_var, width=20).grid(row=1, column=1, sticky='w', padx=(5, 0))

        # 状态
        ttk.Label(basic_frame, text="商品状态:").grid(row=2, column=0, sticky='w', pady=2)
        self.product_status_var = tk.StringVar()
        status_combo = ttk.Combobox(basic_frame, textvariable=self.product_status_var,
                                   values=['available', 'sold', 'reserved'], width=18)
        status_combo.grid(row=2, column=1, sticky='w', padx=(5, 0))

        basic_frame.columnconfigure(1, weight=1)

        # 商品描述
        desc_frame = ttk.LabelFrame(parent, text="商品描述", padding=10)
        desc_frame.pack(fill='both', expand=True)

        self.product_desc_text = scrolledtext.ScrolledText(desc_frame, height=6, wrap='word')
        self.product_desc_text.pack(fill='both', expand=True)

    def create_strategy_config_tab(self, parent):
        """创建策略配置标签页"""
        # 价格策略
        price_frame = ttk.LabelFrame(parent, text="价格策略", padding=10)
        price_frame.pack(fill='x', pady=(0, 10))

        # 最低价格
        ttk.Label(price_frame, text="最低价格:").grid(row=0, column=0, sticky='w', pady=2)
        self.min_price_var = tk.StringVar()
        ttk.Entry(price_frame, textvariable=self.min_price_var, width=15).grid(row=0, column=1, sticky='w', padx=(5, 0))

        # 最大折扣
        ttk.Label(price_frame, text="最大折扣(%):").grid(row=0, column=2, sticky='w', pady=2, padx=(20, 0))
        self.max_discount_var = tk.StringVar()
        ttk.Entry(price_frame, textvariable=self.max_discount_var, width=15).grid(row=0, column=3, sticky='w', padx=(5, 0))

        # 议价轮数
        ttk.Label(price_frame, text="议价轮数:").grid(row=1, column=0, sticky='w', pady=2)
        self.bargain_rounds_var = tk.StringVar()
        ttk.Entry(price_frame, textvariable=self.bargain_rounds_var, width=15).grid(row=1, column=1, sticky='w', padx=(5, 0))

        # 自动回复开关
        self.auto_reply_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(price_frame, text="启用自动回复", variable=self.auto_reply_var).grid(row=1, column=2, sticky='w', padx=(20, 0))

        # 回复模板
        template_frame = ttk.LabelFrame(parent, text="回复模板 (针对性模式)", padding=10)
        template_frame.pack(fill='both', expand=True)

        # 创建模板notebook
        template_notebook = ttk.Notebook(template_frame)
        template_notebook.pack(fill='both', expand=True)

        # 问候模板
        greeting_frame = ttk.Frame(template_notebook)
        template_notebook.add(greeting_frame, text="问候")
        self.greeting_template_text = scrolledtext.ScrolledText(greeting_frame, height=3, wrap='word')
        self.greeting_template_text.pack(fill='both', expand=True, padx=5, pady=5)

        # 价格回复模板
        price_template_frame = ttk.Frame(template_notebook)
        template_notebook.add(price_template_frame, text="价格回复")
        self.price_template_text = scrolledtext.ScrolledText(price_template_frame, height=3, wrap='word')
        self.price_template_text.pack(fill='both', expand=True, padx=5, pady=5)

        # 技术回复模板
        tech_template_frame = ttk.Frame(template_notebook)
        template_notebook.add(tech_template_frame, text="技术回复")
        self.tech_template_text = scrolledtext.ScrolledText(tech_template_frame, height=3, wrap='word')
        self.tech_template_text.pack(fill='both', expand=True, padx=5, pady=5)

        # 保存按钮
        button_frame = ttk.Frame(parent)
        button_frame.pack(fill='x', pady=(10, 0))

        ttk.Button(button_frame, text="💾 保存策略", command=self.save_strategy).pack(side='right', padx=5)
        ttk.Button(button_frame, text="🔄 重置", command=self.reset_strategy).pack(side='right', padx=5)

    def on_mode_change(self):
        """模式切换事件"""
        mode = self.reply_mode.get()
        if mode == "general":
            messagebox.showinfo("通用模式", "已切换到通用模式\n\nAI将自动分析商品信息并生成智能回复，无需手动配置策略。")
        else:
            messagebox.showinfo("针对性模式", "已切换到针对性模式\n\n您可以为每个商品设置自定义的回复策略和模板。")

    def refresh_products(self):
        """刷新商品列表"""
        if not self.product_manager:
            return

        # 清空现有项目
        for item in self.product_tree.get_children():
            self.product_tree.delete(item)

        try:
            # 从数据库获取商品列表
            products = self.product_manager.get_all_products()

            for product in products:
                # 获取该商品的策略模式
                strategy = self.product_manager.get_reply_strategy(product.item_id)
                mode = strategy.mode if strategy else "general"

                # 插入到树形控件
                self.product_tree.insert('', 'end', values=(
                    product.item_id,
                    product.title,
                    f"¥{product.price:.2f}",
                    product.stock_status,
                    mode
                ))

            # 更新选中数量显示
            self.selected_count_var.set("已选中: 0 个商品")

        except Exception as e:
            messagebox.showerror("错误", f"刷新商品列表失败: {e}")

    def search_product(self, event=None):
        """搜索商品"""
        keyword = self.product_id_var.get().strip()

        if not self.product_manager:
            return

        # 清空现有项目
        for item in self.product_tree.get_children():
            self.product_tree.delete(item)

        try:
            if keyword:
                # 搜索商品
                products = self.product_manager.search_products(keyword)
            else:
                # 如果搜索框为空，显示所有商品
                products = self.product_manager.get_all_products()

            for product in products:
                # 获取该商品的策略模式
                strategy = self.product_manager.get_reply_strategy(product.item_id)
                mode = strategy.mode if strategy else "general"

                # 插入到树形控件
                self.product_tree.insert('', 'end', values=(
                    product.item_id,
                    product.title,
                    f"¥{product.price:.2f}",
                    product.stock_status,
                    mode
                ))

            # 更新选中数量显示
            self.selected_count_var.set("已选中: 0 个商品")

            if keyword and not products:
                messagebox.showinfo("搜索结果", f"未找到包含 '{keyword}' 的商品")

        except Exception as e:
            messagebox.showerror("错误", f"搜索失败: {e}")

    def add_product(self):
        """添加新商品"""
        item_id = self.product_id_var.get().strip()
        if not item_id:
            messagebox.showwarning("警告", "请输入商品ID")
            return

        # 打开添加商品对话框
        self.open_add_product_dialog(item_id)

    def on_product_select(self, event):
        """商品选择事件"""
        selection = self.product_tree.selection()

        # 更新选中数量显示
        count = len(selection)
        self.selected_count_var.set(f"已选中: {count} 个商品")

        if not selection:
            return

        # 如果只选中一个商品，加载其详细信息
        if len(selection) == 1:
            item = self.product_tree.item(selection[0])
            item_id = item['values'][0]

            if self.product_manager:
                try:
                    product = self.product_manager.get_product(item_id)
                    if product:
                        self.load_product_info(product)

                    strategy = self.product_manager.get_reply_strategy(item_id)
                    if strategy:
                        self.load_strategy_info(strategy)
                except Exception as e:
                    messagebox.showerror("错误", f"加载商品信息失败: {e}")
        else:
            # 多选时清空详细信息显示
            self.clear_product_info()

    def on_product_double_click(self, event):
        """商品双击事件 - 编辑商品"""
        selection = self.product_tree.selection()
        if len(selection) == 1:
            item = self.product_tree.item(selection[0])
            item_id = item['values'][0]
            self.edit_product(item_id)

    def clear_product_info(self):
        """清空商品信息显示"""
        self.product_title_var.set("")
        self.product_price_var.set("")
        self.product_status_var.set("available")
        self.product_desc_text.delete('1.0', tk.END)

        # 清空策略信息
        self.reply_mode.set("general")
        self.min_price_var.set("")
        self.max_discount_var.set("10")
        self.bargain_rounds_var.set("3")
        self.auto_reply_var.set(True)

    def load_product_info(self, product: ProductInfo):
        """加载商品信息到界面"""
        self.product_title_var.set(product.title)
        self.product_price_var.set(str(product.price))
        self.product_status_var.set(product.stock_status)

        # 清空并设置描述
        self.product_desc_text.delete('1.0', tk.END)
        self.product_desc_text.insert('1.0', product.description)

    def load_strategy_info(self, strategy: ReplyStrategy):
        """加载策略信息到界面"""
        self.reply_mode.set(strategy.mode)
        self.min_price_var.set(str(strategy.min_price))
        self.max_discount_var.set(str(strategy.max_discount_percent))
        self.bargain_rounds_var.set(str(strategy.bargain_rounds))
        self.auto_reply_var.set(strategy.auto_reply_enabled)

        # 加载模板
        self.greeting_template_text.delete('1.0', tk.END)
        self.greeting_template_text.insert('1.0', strategy.greeting_template)

        self.price_template_text.delete('1.0', tk.END)
        self.price_template_text.insert('1.0', strategy.price_response_template)

        self.tech_template_text.delete('1.0', tk.END)
        self.tech_template_text.insert('1.0', strategy.tech_response_template)

    def save_strategy(self):
        """保存策略配置"""
        selection = self.product_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择一个商品")
            return

        item = self.product_tree.item(selection[0])
        item_id = item['values'][0]

        try:
            # 创建策略对象
            strategy = ReplyStrategy(
                item_id=item_id,
                mode=self.reply_mode.get(),
                min_price=float(self.min_price_var.get() or 0),
                max_discount_percent=float(self.max_discount_var.get() or 10),
                bargain_rounds=int(self.bargain_rounds_var.get() or 3),
                greeting_template=self.greeting_template_text.get('1.0', tk.END).strip(),
                price_response_template=self.price_template_text.get('1.0', tk.END).strip(),
                tech_response_template=self.tech_template_text.get('1.0', tk.END).strip(),
                auto_reply_enabled=self.auto_reply_var.get()
            )

            # 保存到数据库
            if self.product_manager.save_reply_strategy(strategy):
                messagebox.showinfo("成功", "策略配置已保存")
                self.refresh_products()
            else:
                messagebox.showerror("错误", "保存策略配置失败")

        except ValueError as e:
            messagebox.showerror("错误", f"输入格式错误: {e}")
        except Exception as e:
            messagebox.showerror("错误", f"保存失败: {e}")

    def reset_strategy(self):
        """重置策略配置"""
        self.min_price_var.set("0")
        self.max_discount_var.set("10")
        self.bargain_rounds_var.set("3")
        self.auto_reply_var.set(True)

        self.greeting_template_text.delete('1.0', tk.END)
        self.price_template_text.delete('1.0', tk.END)
        self.tech_template_text.delete('1.0', tk.END)

    def open_add_product_dialog(self, item_id: str):
        """打开添加商品对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("添加商品")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 商品ID（只读）
        ttk.Label(dialog, text="商品ID:").grid(row=0, column=0, sticky='w', padx=10, pady=5)
        ttk.Label(dialog, text=item_id, font=('Arial', 10, 'bold')).grid(row=0, column=1, sticky='w', padx=10)

        # 标题
        ttk.Label(dialog, text="商品标题:").grid(row=1, column=0, sticky='w', padx=10, pady=5)
        title_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=title_var, width=40).grid(row=1, column=1, sticky='ew', padx=10)

        # 价格
        ttk.Label(dialog, text="价格:").grid(row=2, column=0, sticky='w', padx=10, pady=5)
        price_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=price_var, width=20).grid(row=2, column=1, sticky='w', padx=10)

        # 描述
        ttk.Label(dialog, text="描述:").grid(row=3, column=0, sticky='nw', padx=10, pady=5)
        desc_text = scrolledtext.ScrolledText(dialog, width=40, height=8)
        desc_text.grid(row=3, column=1, sticky='ew', padx=10, pady=5)

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.grid(row=4, column=0, columnspan=2, pady=20)

        def save_product():
            try:
                product = ProductInfo(
                    item_id=item_id,
                    title=title_var.get().strip(),
                    price=float(price_var.get() or 0),
                    description=desc_text.get('1.0', tk.END).strip()
                )

                if self.product_manager.save_product(product):
                    messagebox.showinfo("成功", "商品已添加")
                    dialog.destroy()
                    self.refresh_products()
                else:
                    messagebox.showerror("错误", "添加商品失败")
            except ValueError:
                messagebox.showerror("错误", "价格格式错误")
            except Exception as e:
                messagebox.showerror("错误", f"添加失败: {e}")

        ttk.Button(button_frame, text="保存", command=save_product).pack(side='left', padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side='left', padx=5)

        dialog.columnconfigure(1, weight=1)

    def select_all_products(self):
        """全选商品"""
        children = self.product_tree.get_children()
        self.product_tree.selection_set(children)
        self.selected_count_var.set(f"已选中: {len(children)} 个商品")

    def deselect_all_products(self):
        """取消全选"""
        self.product_tree.selection_remove(self.product_tree.selection())
        self.selected_count_var.set("已选中: 0 个商品")
        self.clear_product_info()

    def delete_selected_products(self):
        """删除选中的商品"""
        selection = self.product_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的商品")
            return

        # 确认删除
        count = len(selection)
        if count == 1:
            item = self.product_tree.item(selection[0])
            product_title = item['values'][1]
            confirm_msg = f"确定要删除商品 '{product_title}' 吗？\n\n此操作不可撤销！"
        else:
            confirm_msg = f"确定要删除选中的 {count} 个商品吗？\n\n此操作不可撤销！"

        if not messagebox.askyesno("确认删除", confirm_msg):
            return

        if not self.product_manager:
            messagebox.showerror("错误", "商品管理器未初始化")
            return

        # 执行删除
        deleted_count = 0
        failed_items = []

        for item_id in selection:
            try:
                item = self.product_tree.item(item_id)
                product_id = item['values'][0]

                # 删除商品和相关策略
                if self.delete_product_from_db(product_id):
                    self.product_tree.delete(item_id)
                    deleted_count += 1
                else:
                    failed_items.append(item['values'][1])  # 商品标题

            except Exception as e:
                failed_items.append(f"ID:{product_id} ({str(e)})")

        # 显示结果
        if deleted_count > 0:
            messagebox.showinfo("删除完成", f"成功删除 {deleted_count} 个商品")

        if failed_items:
            messagebox.showerror("删除失败", f"以下商品删除失败：\n" + "\n".join(failed_items))

        # 更新选中数量显示
        self.selected_count_var.set("已选中: 0 个商品")
        self.clear_product_info()

    def delete_product_from_db(self, item_id: str) -> bool:
        """从数据库删除商品及其策略"""
        try:
            return self.product_manager.delete_product(item_id)
        except Exception as e:
            logger.error(f"删除商品失败: {e}")
            return False

    def batch_set_strategy(self):
        """批量设置策略"""
        selection = self.product_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要设置策略的商品")
            return

        # 创建批量设置对话框
        dialog = tk.Toplevel(self.root)
        dialog.title("批量设置策略")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 标题
        title_label = ttk.Label(dialog, text=f"为 {len(selection)} 个商品批量设置策略", font=('Arial', 12, 'bold'))
        title_label.pack(pady=10)

        # 策略配置框架
        config_frame = ttk.LabelFrame(dialog, text="策略配置", padding=10)
        config_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # 模式选择
        ttk.Label(config_frame, text="回复模式:").grid(row=0, column=0, sticky='w', pady=5)
        batch_mode_var = tk.StringVar(value="general")
        mode_frame = ttk.Frame(config_frame)
        mode_frame.grid(row=0, column=1, sticky='w', pady=5)
        ttk.Radiobutton(mode_frame, text="通用模式", variable=batch_mode_var, value="general").pack(side='left')
        ttk.Radiobutton(mode_frame, text="针对模式", variable=batch_mode_var, value="specific").pack(side='left', padx=(10, 0))

        # 价格设置
        ttk.Label(config_frame, text="最低价格:").grid(row=1, column=0, sticky='w', pady=5)
        batch_min_price_var = tk.StringVar()
        ttk.Entry(config_frame, textvariable=batch_min_price_var, width=20).grid(row=1, column=1, sticky='w', pady=5)

        ttk.Label(config_frame, text="最大折扣(%):").grid(row=2, column=0, sticky='w', pady=5)
        batch_discount_var = tk.StringVar(value="10")
        ttk.Entry(config_frame, textvariable=batch_discount_var, width=20).grid(row=2, column=1, sticky='w', pady=5)

        ttk.Label(config_frame, text="议价轮数:").grid(row=3, column=0, sticky='w', pady=5)
        batch_rounds_var = tk.StringVar(value="3")
        ttk.Entry(config_frame, textvariable=batch_rounds_var, width=20).grid(row=3, column=1, sticky='w', pady=5)

        # 自动回复
        batch_auto_reply_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(config_frame, text="启用自动回复", variable=batch_auto_reply_var).grid(row=4, column=0, columnspan=2, sticky='w', pady=5)

        def apply_batch_strategy():
            """应用批量策略"""
            try:
                applied_count = 0
                failed_items = []

                for item_id in selection:
                    try:
                        item = self.product_tree.item(item_id)
                        product_id = item['values'][0]

                        # 创建策略对象
                        from smart_product_manager import ReplyStrategy
                        strategy = ReplyStrategy(
                            item_id=product_id,
                            mode=batch_mode_var.get(),
                            min_price=float(batch_min_price_var.get()) if batch_min_price_var.get() else 0.0,
                            max_discount_percent=float(batch_discount_var.get()),
                            bargain_rounds=int(batch_rounds_var.get()),
                            auto_reply_enabled=batch_auto_reply_var.get()
                        )

                        # 保存策略
                        if self.product_manager.save_reply_strategy(strategy):
                            applied_count += 1
                        else:
                            failed_items.append(item['values'][1])

                    except Exception as e:
                        failed_items.append(f"{item['values'][1]} ({str(e)})")

                # 显示结果
                if applied_count > 0:
                    messagebox.showinfo("批量设置完成", f"成功为 {applied_count} 个商品设置策略")

                if failed_items:
                    messagebox.showerror("部分设置失败", f"以下商品设置失败：\n" + "\n".join(failed_items))

                dialog.destroy()
                self.refresh_products()

            except ValueError as e:
                messagebox.showerror("输入错误", "请检查数值输入格式")
            except Exception as e:
                messagebox.showerror("错误", f"批量设置失败: {e}")

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=20)

        ttk.Button(button_frame, text="应用设置", command=apply_batch_strategy).pack(side='left', padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side='left', padx=5)

    def edit_product(self, item_id: str):
        """编辑商品信息"""
        if not self.product_manager:
            messagebox.showerror("错误", "商品管理器未初始化")
            return

        try:
            product = self.product_manager.get_product(item_id)
            if not product:
                messagebox.showerror("错误", "商品不存在")
                return

            # 加载商品信息到编辑界面
            self.load_product_info(product)

            # 切换到详细信息标签页
            self.detail_notebook.select(0)  # 选择第一个标签页（商品信息）

            messagebox.showinfo("编辑模式", f"已加载商品 '{product.title}' 的信息\n\n请在右侧面板中编辑商品信息，然后点击保存。")

        except Exception as e:
            messagebox.showerror("错误", f"加载商品信息失败: {e}")

def main():
    """主函数"""
    print("🚀 启动XianyuAutoAgent配置界面...")

    try:
        app = ConfigGUI()
        app.run()
    except Exception as e:
        print(f"启动失败：{e}")
        return 1

    return 0

if __name__ == "__main__":
    sys.exit(main())
