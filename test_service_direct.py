#!/usr/bin/env python3
"""
直接测试服务启动
"""

import os
import sys
import time
from dotenv import load_dotenv

def test_service_startup():
    """测试服务启动过程"""
    print("🧪 直接测试服务启动...")
    
    try:
        # 加载环境变量
        load_dotenv()
        
        # 检查基本配置
        print("\n📋 配置检查:")
        api_key = os.getenv("API_KEY", "")
        cookies = os.getenv("COOKIES_STR", "")
        
        print(f"  API_KEY: {'✅ 已配置' if api_key else '❌ 未配置'}")
        print(f"  COOKIES: {'✅ 已配置' if cookies else '❌ 未配置'}")
        
        if not api_key or not cookies:
            print("❌ 基本配置缺失，无法启动服务")
            return False
        
        # 导入主模块
        print("\n🔧 导入主模块...")
        import main
        print("✅ 主模块导入成功")
        
        # 测试配置管理器
        print("\n⚙️ 测试配置管理器...")
        config = main.config
        print(f"  API_KEY: {'✅' if config.ai.api_key else '❌'}")
        print(f"  BASE_URL: {config.ai.base_url}")
        print(f"  MODEL: {config.ai.model_name}")
        print(f"  COOKIES: {'✅' if config.security.cookies_str else '❌'}")
        
        # 测试AI客户端管理器
        print("\n🤖 测试AI客户端管理器...")
        ai_manager = main.AIClientManager()
        client = ai_manager.client
        print("✅ AI客户端初始化成功")
        
        # 测试智能代理
        print("\n🧠 测试智能代理初始化...")
        try:
            smart_agent = main.SmartXianyuAgent()
            print("✅ 智能代理初始化成功")
        except Exception as e:
            print(f"❌ 智能代理初始化失败: {e}")
            print("尝试传统模式...")
            traditional_bot = main.XianyuReplyBot()
            print("✅ 传统模式初始化成功")
        
        # 测试XianyuLive初始化
        print("\n🌐 测试XianyuLive初始化...")
        cookies_str = config.security.cookies_str
        xianyu_live = main.XianyuLive(cookies_str)
        print("✅ XianyuLive初始化成功")
        print(f"  用户ID: {xianyu_live.myid}")
        print(f"  设备ID: {xianyu_live.device_id}")
        print(f"  WebSocket URL: {xianyu_live.base_url}")
        
        print("\n🎉 所有组件初始化测试通过！")
        print("\n💡 建议:")
        print("1. 组件初始化正常，问题可能在WebSocket连接")
        print("2. 检查网络连接和Cookie有效性")
        print("3. 运行 python main.py --service 查看详细日志")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_websocket_connection():
    """测试WebSocket连接"""
    print("\n🔗 测试WebSocket连接...")
    
    try:
        import asyncio
        import websockets
        from dotenv import load_dotenv
        
        load_dotenv()
        cookies_str = os.getenv("COOKIES_STR", "")
        
        if not cookies_str:
            print("❌ Cookie未配置，无法测试WebSocket连接")
            return False
        
        async def test_connection():
            try:
                base_url = "wss://wss-goofish.dingtalk.com/ws"
                headers = {
                    "Cookie": cookies_str,
                    "Host": "wss-goofish.dingtalk.com",
                    "Connection": "Upgrade",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                    "Origin": "https://www.goofish.com",
                }
                
                print(f"🔗 尝试连接: {base_url}")
                
                # 设置较短的超时时间
                async with websockets.connect(
                    base_url, 
                    extra_headers=headers,
                    ping_timeout=10,
                    close_timeout=5
                ) as websocket:
                    print("✅ WebSocket连接成功")
                    
                    # 等待一小段时间看是否有消息
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        print(f"📨 收到消息: {message[:100]}...")
                    except asyncio.TimeoutError:
                        print("⏰ 5秒内未收到消息（正常）")
                    
                    return True
                    
            except websockets.exceptions.InvalidURI as e:
                print(f"❌ WebSocket URI无效: {e}")
                return False
            except websockets.exceptions.InvalidHandshake as e:
                print(f"❌ WebSocket握手失败: {e}")
                print("💡 可能原因: Cookie无效或已过期")
                return False
            except Exception as e:
                print(f"❌ WebSocket连接失败: {e}")
                return False
        
        # 运行异步测试
        result = asyncio.run(test_connection())
        return result
        
    except Exception as e:
        print(f"❌ WebSocket测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🔍 XianyuAutoAgent 服务启动诊断")
    print("=" * 50)
    
    # 测试服务组件
    component_test = test_service_startup()
    
    if component_test:
        # 测试WebSocket连接
        websocket_test = test_websocket_connection()
        
        if websocket_test:
            print("\n🎉 所有测试通过！服务应该可以正常启动。")
        else:
            print("\n⚠️ WebSocket连接测试失败，请检查Cookie有效性。")
    else:
        print("\n❌ 组件测试失败，请检查配置。")
    
    input("\n按回车键退出...")
