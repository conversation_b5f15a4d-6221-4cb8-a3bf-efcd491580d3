# 🌐 Web配置管理界面使用指南

## 📖 简介

XianyuAutoAgent现在提供了友好的Web配置管理界面，让您可以通过浏览器轻松配置所有设置，无需手动编辑配置文件。

## 🚀 快速启动

### 方法一：使用启动脚本（推荐）
```bash
python start_config.py
```

### 方法二：直接启动
```bash
python web_config.py
```

启动后，配置界面将自动在浏览器中打开：`http://localhost:5000`

## 🔧 配置界面功能

### 1. 基础配置 🔑
- **OpenAI API密钥**: 您的AI服务API密钥
- **闲鱼Cookie字符串**: 从浏览器获取的认证信息
- **加密密钥**: 用于数据加密（可选，留空自动生成）

### 2. AI设置 🧠
- **API基础URL**: OpenAI API服务地址
- **AI模型**: 选择使用的AI模型（GPT-4o Mini推荐）
- **创造性温度**: 控制回复的创新程度（0-2）
- **最大令牌数**: 单次回复的最大长度

### 3. 提示词配置 📝
- **分类提示词**: 用于判断消息类型
- **价格咨询提示词**: 处理价格相关问题
- **技术咨询提示词**: 处理技术问题
- **默认提示词**: 处理一般咨询

### 4. 高级设置 ⚙️
- **WebSocket配置**: 连接设置
- **数据库配置**: 数据存储设置
- **日志配置**: 日志级别和文件管理

## 📋 使用步骤

### 第一次配置
1. 启动Web配置界面
2. 在"基础配置"中填入必需的API密钥和Cookie
3. 根据需要调整"AI设置"
4. 自定义"提示词"以适应您的业务需求
5. 点击"💾 保存配置"

### 测试配置
1. 填写完配置后，点击"🧪 测试配置"
2. 系统会验证配置的有效性
3. 如有问题，会显示具体的错误信息

### 重新加载配置
- 点击"🔄 重新加载"可以从文件重新读取配置
- 适用于外部修改了配置文件的情况

## 🔒 获取闲鱼Cookie

1. 打开浏览器，登录闲鱼网站
2. 按F12打开开发者工具
3. 切换到"Network"（网络）标签
4. 刷新页面或进行任何操作
5. 找到任意一个请求，查看Request Headers
6. 复制完整的Cookie字符串
7. 粘贴到配置界面的"闲鱼Cookie字符串"字段

## 📁 配置文件说明

Web界面会自动管理以下文件：

### 环境配置文件
- `.env` - 存储所有环境变量配置

### 提示词文件
- `prompts/classify_prompt.txt` - 分类提示词
- `prompts/price_prompt.txt` - 价格咨询提示词
- `prompts/tech_prompt.txt` - 技术咨询提示词
- `prompts/default_prompt.txt` - 默认提示词

## 🛡️ 安全注意事项

1. **API密钥安全**: 
   - 不要在公共场所使用配置界面
   - 配置完成后及时关闭浏览器

2. **Cookie安全**:
   - Cookie包含您的登录信息，请妥善保管
   - 定期更新Cookie以保持有效性

3. **网络安全**:
   - 配置界面默认只在本地访问（localhost）
   - 不要在不安全的网络环境中使用

## 🔧 故障排除

### 配置界面无法打开
1. 检查是否安装了Flask: `pip install flask==3.0.0`
2. 检查端口5000是否被占用
3. 查看控制台错误信息

### 配置保存失败
1. 检查文件权限，确保可以写入.env文件
2. 检查prompts目录是否存在且可写
3. 验证配置格式是否正确

### API密钥无效
1. 确认API密钥格式正确
2. 检查API密钥是否有足够的额度
3. 验证API基础URL是否正确

### Cookie失效
1. 重新从浏览器获取Cookie
2. 确认闲鱼账号仍然有效
3. 检查Cookie格式是否完整

## 🎯 最佳实践

1. **定期备份配置**: 保存.env文件和prompts目录的备份
2. **测试配置**: 每次修改后都进行配置测试
3. **监控日志**: 关注日志文件中的错误信息
4. **渐进式配置**: 先配置基础功能，再逐步完善高级设置

## 📞 技术支持

如果您在使用Web配置界面时遇到问题：

1. 查看控制台输出的错误信息
2. 检查logs目录中的日志文件
3. 确认所有依赖都已正确安装
4. 参考本文档的故障排除部分

---

🎉 **恭喜！** 您现在可以通过友好的Web界面轻松管理XianyuAutoAgent的所有配置了！
