#!/usr/bin/env python3
"""
简单的配置测试脚本
"""

print("开始测试配置...")

try:
    print("1. 导入配置模块...")
    from config import config
    print("✅ 配置模块导入成功")
    
    print("2. 检查配置对象...")
    print(f"   - 数据库路径: {config.database.path}")
    print(f"   - 日志级别: {config.log.level}")
    print(f"   - WebSocket URL: {config.websocket.base_url}")
    print("✅ 配置对象访问正常")
    
    print("3. 测试配置验证...")
    validation_result = config.validate_all()
    if validation_result:
        print("✅ 所有配置验证通过")
    else:
        print("⚠️ 部分配置验证失败，但程序可以继续运行")
    
    print("4. 测试日志系统...")
    from loguru import logger
    logger.info("这是一条测试日志")
    print("✅ 日志系统正常")
    
    print("\n🎉 配置测试完成！")
    
except Exception as e:
    print(f"❌ 配置测试失败: {e}")
    import traceback
    traceback.print_exc()
