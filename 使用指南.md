# XianyuAutoAgent 使用指南

## 🎯 快速开始

### 一键启动（推荐）
```bash
python main.py
```
运行后会弹出启动选择界面，您可以：
- 🔧 **打开配置界面** - 设置所有参数
- ▶️ **直接启动服务** - 开始智能客服
- ❌ **退出程序**

## 🔧 配置管理

### 首次配置
1. 运行 `python main.py`
2. 选择 "🔧 打开配置界面"
3. 在 "🔑 基础配置" 标签页设置：
   - **Cookie字符串** - 从浏览器复制闲鱼Cookie
   - **AI模型配置** - 设置DeepSeek API密钥
   - **日志级别** - 选择DEBUG或INFO

### 智能商品管理
在 "🛍️ 智能商品" 标签页可以：
- **添加商品策略** - 为特定商品设置回复策略
- **多选批量操作** - 选择多个商品进行批量删除或设置
- **搜索商品** - 快速查找特定商品
- **编辑策略** - 双击商品进行编辑

### 模式选择
- **通用模式** - 对所有商品使用统一策略
- **针对回复模式** - 为不同商品设置专门策略

## 🚀 启动服务

### 方式一：通过启动界面
1. 运行 `python main.py`
2. 选择 "▶️ 直接启动服务"

### 方式二：命令行直接启动
```bash
python main.py --service
```

### 方式三：传统启动（兼容）
```bash
python config_gui.py  # 仅配置界面
```

## 📋 功能特性

### 智能回复
- **自动检测商品ID** - 无需手动输入
- **价格智能处理** - 自动获取商品价格信息
- **上下文理解** - 记住对话历史
- **议价处理** - 智能应对价格谈判

### 多选批量操作
- **Ctrl+点击** - 选择多个商品
- **Shift+点击** - 范围选择
- **批量删除** - 一次删除多个商品策略
- **批量设置** - 统一设置多个商品的策略

### 人工接管
- **关键词切换** - 发送特定关键词切换模式
- **自动超时** - 人工模式自动恢复
- **状态显示** - 清晰显示当前模式

## ⚙️ 高级配置

### AI模型设置
```
API地址: https://api.deepseek.com/v1
模型名称: deepseek-chat 或 deepseek-reasoner
API密钥: 您的DeepSeek API Key
```

### Cookie获取方法
1. 打开浏览器，登录闲鱼网站
2. 按F12打开开发者工具
3. 切换到Network标签
4. 刷新页面，找到任意请求
5. 复制Cookie字段的完整内容

### 环境变量配置
创建 `.env` 文件：
```
COOKIES_STR=您的Cookie字符串
DEEPSEEK_API_KEY=您的API密钥
LOG_LEVEL=INFO
```

## 🔍 故障排除

### 常见问题

**1. 界面无法显示**
- 检查Python是否安装tkinter
- Windows: 通常自带
- Linux: `sudo apt-get install python3-tk`

**2. Cookie失效**
- 重新获取Cookie字符串
- 检查闲鱼账号是否正常登录

**3. AI回复失败**
- 检查API密钥是否正确
- 确认网络连接正常
- 查看日志获取详细错误信息

**4. 商品信息获取失败**
- 检查商品ID是否正确
- 确认商品仍在售卖状态

### 日志查看
- 启动时会显示详细日志
- 错误信息会标红显示
- 可调整日志级别获取更多信息

## 📊 使用技巧

### 提高效率
1. **批量配置** - 使用多选功能批量设置商品策略
2. **模板复用** - 设置好的策略可以批量应用
3. **快速启动** - 配置完成后使用 `python main.py --service`

### 最佳实践
1. **定期更新Cookie** - 避免认证失效
2. **合理设置策略** - 根据商品特点设置不同策略
3. **监控日志** - 及时发现和解决问题

### 安全建议
1. **保护API密钥** - 不要泄露给他人
2. **定期检查** - 监控账号异常活动
3. **备份配置** - 定期备份重要配置

## 🎯 命令行参数

```bash
python main.py           # 显示启动选择界面
python main.py --config  # 直接打开配置界面
python main.py --service # 直接启动服务
python main.py --help    # 显示帮助信息
```

## 📞 技术支持

### 获取帮助
- 查看日志输出了解详细错误
- 检查配置文件是否正确
- 确认网络连接和API服务状态

### 功能建议
如需新功能或改进建议，可以：
- 查看现有配置选项
- 测试不同参数组合
- 记录使用过程中的问题

---

**版本**: 集成版本  
**更新**: 2025-06-28  
**状态**: ✅ 功能完整，可正常使用
