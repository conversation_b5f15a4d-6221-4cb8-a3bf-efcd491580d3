#!/usr/bin/env python3
"""
直接测试配置界面启动按钮
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.getcwd())

try:
    from config_gui import ConfigGUI
    
    print("🚀 启动配置界面...")
    print("请查看配置界面底部是否有 '🚀 启动服务' 按钮")
    print("按钮应该位于底部中间位置")
    print("=" * 50)
    
    # 创建并运行配置界面
    gui = ConfigGUI()
    gui.run()
    
except Exception as e:
    print(f"❌ 启动配置界面失败: {e}")
    import traceback
    traceback.print_exc()
