#!/usr/bin/env python3
"""
改进的配置加载器 - 解决配置冲突问题
用于替换config_gui.py中的配置加载逻辑
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, Tuple, List
from dotenv import load_dotenv, set_key

# 导入统一配置管理器
sys.path.append(str(Path(__file__).parent))
from main import config, UnifiedConfigManager


class ImprovedConfigLoader:
    """改进的配置加载器"""
    
    def __init__(self):
        # 使用全局统一配置管理器
        self.config_manager = config
        
        # 标准化的配置字段映射
        self.config_fields = {
            # GUI字段名 -> 环境变量名
            "api_key": "API_KEY",
            "base_url": "BASE_URL", 
            "model": "MODEL",
            "temperature": "TEMPERATURE",
            "max_tokens": "MAX_TOKENS",
            "top_p": "TOP_P",
            "cookies_str": "COOKIES_STR",
            "encryption_key": "ENCRYPTION_KEY",
            "ws_base_url": "WS_BASE_URL",
            "ws_timeout": "WS_TIMEOUT",
            "database_path": "DATABASE_PATH",
            "log_level": "LOG_LEVEL",
            "log_file_path": "LOG_FILE_PATH"
        }
        
        # 默认值映射
        self.defaults = {
            "API_KEY": "",
            "BASE_URL": "https://api.deepseek.com/v1",
            "MODEL": "deepseek-chat",
            "TEMPERATURE": "0.7",
            "MAX_TOKENS": "2000",
            "TOP_P": "0.8",
            "COOKIES_STR": "",
            "ENCRYPTION_KEY": "",
            "WS_BASE_URL": "wss://wss-goofish.dingtalk.com/",
            "WS_TIMEOUT": "30",
            "DATABASE_PATH": "data/chat_history.db",
            "LOG_LEVEL": "INFO",
            "LOG_FILE_PATH": "logs/xianyu_agent.log"
        }
    
    def load_all_config(self) -> Dict[str, Any]:
        """加载所有配置"""
        try:
            # 确保目录存在
            Path("prompts").mkdir(exist_ok=True)
            Path("data").mkdir(exist_ok=True)
            Path("logs").mkdir(exist_ok=True)
            
            # 使用统一配置管理器加载
            config_data = {}
            
            for gui_field, env_var in self.config_fields.items():
                value = self.config_manager.get_env(env_var, self.defaults.get(env_var, ""))
                config_data[gui_field] = value
            
            # 加载提示词
            config_data["prompts"] = self.load_prompts()
            
            return config_data
            
        except Exception as e:
            raise Exception(f"配置加载失败：{str(e)}")
    
    def save_all_config(self, config_data: Dict[str, Any]) -> Tuple[bool, str]:
        """保存所有配置"""
        try:
            # 验证必需字段
            validation_result = self.validate_config(config_data)
            if not validation_result[0]:
                return False, validation_result[1]
            
            # 确保.env文件存在
            env_file = Path(".env")
            if not env_file.exists():
                env_file.touch()
            
            # 保存环境配置
            for gui_field, env_var in self.config_fields.items():
                if gui_field in config_data:
                    value = str(config_data[gui_field])
                    set_key(".env", env_var, value)
            
            # 保存提示词文件
            if "prompts" in config_data:
                self.save_prompts(config_data["prompts"])
            
            # 重新加载配置管理器
            self.config_manager.reload_config()
            
            return True, "配置保存成功！"
            
        except Exception as e:
            return False, f"配置保存失败：{str(e)}"
    
    def validate_config(self, config_data: Dict[str, Any]) -> Tuple[bool, str]:
        """验证配置"""
        # 验证API密钥
        if not config_data.get("api_key", "").strip():
            return False, "API密钥不能为空！"
        
        # 验证Cookie
        cookie_str = config_data.get("cookies_str", "").strip()
        if not cookie_str:
            return False, "Cookie字符串不能为空！"
        
        # 验证Cookie格式
        is_valid, missing_fields = self.config_manager.validate_cookie_format(cookie_str)
        if not is_valid:
            return False, f"Cookie中缺少必要字段：{', '.join(missing_fields)}"
        
        # 验证数值类型
        try:
            float(config_data.get("temperature", "0.7"))
            int(config_data.get("max_tokens", "2000"))
            float(config_data.get("top_p", "0.8"))
            int(config_data.get("ws_timeout", "30"))
        except ValueError as e:
            return False, f"数值格式错误：{str(e)}"
        
        return True, "配置验证通过"
    
    def load_prompts(self) -> Dict[str, str]:
        """加载所有提示词"""
        prompt_files = {
            "classify_prompt": "classify_prompt.txt",
            "price_prompt": "price_prompt.txt", 
            "tech_prompt": "tech_prompt.txt",
            "default_prompt": "default_prompt.txt"
        }
        
        prompts_data = {}
        
        for key, filename in prompt_files.items():
            file_path = Path("prompts") / filename
            try:
                if file_path.exists():
                    prompts_data[key] = file_path.read_text(encoding='utf-8')
                else:
                    prompts_data[key] = ""
            except Exception as e:
                print(f"加载提示词文件失败 {filename}: {e}")
                prompts_data[key] = ""
        
        return prompts_data
    
    def save_prompts(self, prompts_data: Dict[str, str]):
        """保存所有提示词"""
        prompt_files = {
            "classify_prompt": "classify_prompt.txt",
            "price_prompt": "price_prompt.txt",
            "tech_prompt": "tech_prompt.txt", 
            "default_prompt": "default_prompt.txt"
        }
        
        for key, filename in prompt_files.items():
            if key in prompts_data:
                file_path = Path("prompts") / filename
                content = prompts_data[key]
                if content:
                    file_path.write_text(content, encoding='utf-8')
    
    def get_config_status(self) -> Tuple[str, str]:
        """获取配置状态"""
        try:
            is_valid, missing_fields = self.config_manager.validate_required_fields()
            if is_valid:
                return "✅ 配置文件已就绪", "green"
            else:
                return f"⚠️ 缺少配置: {', '.join(missing_fields)}", "orange"
        except Exception:
            return "❌ 配置文件异常", "red"
    
    def get_standardized_env_vars(self) -> Dict[str, str]:
        """获取标准化的环境变量映射"""
        return {
            "数据库路径": "DATABASE_PATH (统一标准)",
            "WebSocket URL": "WS_BASE_URL (统一标准)",
            "WebSocket超时": "WS_TIMEOUT (统一标准)",
            "AI Top-P": "TOP_P (统一标准)",
            "默认BASE_URL": "https://api.deepseek.com/v1 (DeepSeek)",
            "默认模型": "deepseek-chat (DeepSeek)"
        }


# 使用示例
def example_usage():
    """使用示例"""
    loader = ImprovedConfigLoader()
    
    # 加载配置
    try:
        config_data = loader.load_all_config()
        print("✅ 配置加载成功")
        print(f"API Key: {config_data['api_key'][:10]}..." if config_data['api_key'] else "未配置")
        print(f"Base URL: {config_data['base_url']}")
        print(f"Model: {config_data['model']}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
    
    # 检查状态
    status_text, status_color = loader.get_config_status()
    print(f"配置状态: {status_text}")
    
    # 显示标准化映射
    print("\n标准化环境变量:")
    for desc, var in loader.get_standardized_env_vars().items():
        print(f"  {desc}: {var}")


if __name__ == "__main__":
    example_usage()
