<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XianyuAutoAgent 配置管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .tab {
            flex: 1;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            background: none;
            font-size: 1.1em;
            font-weight: 500;
        }

        .tab:hover {
            background: #e9ecef;
        }

        .tab.active {
            background: white;
            border-bottom: 3px solid #4facfe;
            color: #4facfe;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1em;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
            font-family: 'Courier New', monospace;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-right: 10px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #4facfe;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .help-text {
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 5px;
        }

        .section-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .tabs {
                flex-direction: column;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 XianyuAutoAgent</h1>
            <p>智能客服配置管理中心</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('basic')">🔧 基础配置</button>
            <button class="tab" onclick="showTab('ai')">🧠 AI设置</button>
            <button class="tab" onclick="showTab('prompts')">📝 提示词</button>
            <button class="tab" onclick="showTab('advanced')">⚙️ 高级设置</button>
        </div>

        <div id="alert" class="alert"></div>
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <p>正在加载配置...</p>
        </div>

        <form id="configForm">
            <!-- 基础配置 -->
            <div id="basic" class="tab-content active">
                <div class="section-title">🔑 认证信息</div>
                
                <div class="form-group">
                    <label for="API_KEY">OpenAI API密钥 *</label>
                    <input type="password" id="API_KEY" name="API_KEY" required>
                    <div class="help-text">您的OpenAI API密钥，用于AI对话功能</div>
                </div>

                <div class="form-group">
                    <label for="COOKIES_STR">闲鱼Cookie字符串 *</label>
                    <textarea id="COOKIES_STR" name="COOKIES_STR" required placeholder="从浏览器开发者工具中复制完整的Cookie字符串"></textarea>
                    <div class="help-text">用于访问闲鱼API的认证信息</div>
                </div>

                <div class="form-group">
                    <label for="ENCRYPTION_KEY">加密密钥</label>
                    <input type="password" id="ENCRYPTION_KEY" name="ENCRYPTION_KEY">
                    <div class="help-text">用于加密敏感数据，留空将自动生成</div>
                </div>
            </div>

            <!-- AI设置 -->
            <div id="ai" class="tab-content">
                <div class="section-title">🧠 AI模型配置</div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="BASE_URL">API基础URL</label>
                        <input type="url" id="BASE_URL" name="BASE_URL" value="https://api.openai.com/v1">
                        <div class="help-text">OpenAI API的基础URL</div>
                    </div>

                    <div class="form-group">
                        <label for="MODEL">AI模型</label>
                        <select id="MODEL" name="MODEL">
                            <option value="gpt-4o-mini">GPT-4o Mini (推荐)</option>
                            <option value="gpt-4o">GPT-4o</option>
                            <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="TEMPERATURE">创造性温度 (0-2)</label>
                        <input type="number" id="TEMPERATURE" name="TEMPERATURE" min="0" max="2" step="0.1" value="0.7">
                        <div class="help-text">控制回复的创造性，0=保守，2=创新</div>
                    </div>

                    <div class="form-group">
                        <label for="MAX_TOKENS">最大令牌数</label>
                        <input type="number" id="MAX_TOKENS" name="MAX_TOKENS" min="100" max="8000" value="2000">
                        <div class="help-text">单次回复的最大长度</div>
                    </div>
                </div>
            </div>

            <!-- 提示词配置 -->
            <div id="prompts" class="tab-content">
                <div class="section-title">📝 AI提示词配置</div>
                
                <div class="form-group">
                    <label for="classify_prompt">分类提示词</label>
                    <textarea id="classify_prompt" name="classify_prompt" placeholder="用于消息分类的提示词..."></textarea>
                    <div class="help-text">用于判断用户消息类型（价格、技术、一般咨询）</div>
                </div>

                <div class="form-group">
                    <label for="price_prompt">价格咨询提示词</label>
                    <textarea id="price_prompt" name="price_prompt" placeholder="处理价格相关咨询的提示词..."></textarea>
                    <div class="help-text">处理价格、议价等相关咨询</div>
                </div>

                <div class="form-group">
                    <label for="tech_prompt">技术咨询提示词</label>
                    <textarea id="tech_prompt" name="tech_prompt" placeholder="处理技术问题的提示词..."></textarea>
                    <div class="help-text">处理产品功能、使用方法等技术问题</div>
                </div>

                <div class="form-group">
                    <label for="default_prompt">默认提示词</label>
                    <textarea id="default_prompt" name="default_prompt" placeholder="处理一般咨询的提示词..."></textarea>
                    <div class="help-text">处理其他类型的一般咨询</div>
                </div>
            </div>

            <!-- 高级设置 -->
            <div id="advanced" class="tab-content">
                <div class="section-title">⚙️ 系统配置</div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="WS_BASE_URL">WebSocket URL</label>
                        <input type="url" id="WS_BASE_URL" name="WS_BASE_URL">
                        <div class="help-text">闲鱼WebSocket连接地址</div>
                    </div>

                    <div class="form-group">
                        <label for="WS_TIMEOUT">WebSocket超时(秒)</label>
                        <input type="number" id="WS_TIMEOUT" name="WS_TIMEOUT" min="10" max="300" value="30">
                    </div>
                </div>

                <div class="form-group">
                    <label for="DATABASE_PATH">数据库路径</label>
                    <input type="text" id="DATABASE_PATH" name="DATABASE_PATH" value="data/chat_history.db">
                    <div class="help-text">聊天历史数据库文件路径</div>
                </div>

                <div class="section-title">📊 日志配置</div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="LOG_LEVEL">日志级别</label>
                        <select id="LOG_LEVEL" name="LOG_LEVEL">
                            <option value="DEBUG">DEBUG</option>
                            <option value="INFO" selected>INFO</option>
                            <option value="WARNING">WARNING</option>
                            <option value="ERROR">ERROR</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="LOG_FILE_PATH">日志文件路径</label>
                        <input type="text" id="LOG_FILE_PATH" name="LOG_FILE_PATH" value="logs/xianyu_agent.log">
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="LOG_ROTATION">日志轮转大小</label>
                        <input type="text" id="LOG_ROTATION" name="LOG_ROTATION" value="100 MB">
                        <div class="help-text">例如: 100 MB, 1 GB</div>
                    </div>

                    <div class="form-group">
                        <label for="LOG_RETENTION">日志保留时间</label>
                        <input type="text" id="LOG_RETENTION" name="LOG_RETENTION" value="30 days">
                        <div class="help-text">例如: 30 days, 1 week</div>
                    </div>
                </div>
            </div>

            <div style="padding: 30px; text-align: center; border-top: 1px solid #dee2e6;">
                <button type="submit" class="btn btn-primary">💾 保存配置</button>
                <button type="button" class="btn btn-secondary" onclick="testConfig()">🧪 测试配置</button>
                <button type="button" class="btn btn-secondary" onclick="loadConfig()">🔄 重新加载</button>
            </div>
        </form>
    </div>

    <script>
        let currentConfig = {};

        // 页面加载时获取配置
        document.addEventListener('DOMContentLoaded', function() {
            loadConfig();
        });

        // 切换标签页
        function showTab(tabName) {
            // 隐藏所有标签内容
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            
            // 移除所有标签的active类
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 显示选中的标签内容
            document.getElementById(tabName).classList.add('active');
            
            // 添加active类到选中的标签
            event.target.classList.add('active');
        }

        // 显示提示信息
        function showAlert(message, type = 'success') {
            const alert = document.getElementById('alert');
            alert.className = `alert alert-${type}`;
            alert.textContent = message;
            alert.style.display = 'block';
            
            setTimeout(() => {
                alert.style.display = 'none';
            }, 5000);
        }

        // 显示加载状态
        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'block' : 'none';
        }

        // 加载配置
        async function loadConfig() {
            showLoading(true);
            
            try {
                const response = await fetch('/api/config');
                const data = await response.json();
                
                if (data.success) {
                    currentConfig = data;
                    
                    // 填充环境配置
                    Object.entries(data.env_config).forEach(([key, value]) => {
                        const element = document.getElementById(key);
                        if (element) {
                            element.value = value;
                        }
                    });
                    
                    // 填充提示词
                    Object.entries(data.prompts).forEach(([key, value]) => {
                        const element = document.getElementById(key);
                        if (element) {
                            element.value = value;
                        }
                    });
                    
                    showAlert('配置加载成功', 'success');
                } else {
                    showAlert('配置加载失败: ' + data.error, 'error');
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }

        // 保存配置
        document.getElementById('configForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            showLoading(true);
            
            try {
                const formData = new FormData(this);
                
                // 构建环境配置
                const envConfig = {};
                const envFields = ['API_KEY', 'BASE_URL', 'MODEL', 'TEMPERATURE', 'MAX_TOKENS', 
                                 'COOKIES_STR', 'ENCRYPTION_KEY', 'WS_BASE_URL', 'WS_TIMEOUT',
                                 'DATABASE_PATH', 'LOG_LEVEL', 'LOG_FILE_PATH', 'LOG_ROTATION', 'LOG_RETENTION'];
                
                envFields.forEach(field => {
                    envConfig[field] = formData.get(field) || '';
                });
                
                // 构建提示词配置
                const prompts = {};
                const promptFields = ['classify_prompt', 'price_prompt', 'tech_prompt', 'default_prompt'];
                
                promptFields.forEach(field => {
                    prompts[field] = formData.get(field) || '';
                });
                
                const response = await fetch('/api/config', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        env_config: envConfig,
                        prompts: prompts
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showAlert('配置保存成功！', 'success');
                } else {
                    if (data.errors) {
                        const errorMessages = Object.values(data.errors).join(', ');
                        showAlert('配置验证失败: ' + errorMessages, 'error');
                    } else {
                        showAlert('配置保存失败: ' + data.error, 'error');
                    }
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        });

        // 测试配置
        async function testConfig() {
            showLoading(true);
            
            try {
                const response = await fetch('/api/test-config', {
                    method: 'POST'
                });
                
                const data = await response.json();
                
                if (data.success) {
                    showAlert('配置测试通过！', 'success');
                } else {
                    showAlert('配置测试失败: ' + data.error, 'error');
                }
            } catch (error) {
                showAlert('网络错误: ' + error.message, 'error');
            } finally {
                showLoading(false);
            }
        }
    </script>
</body>
</html>
