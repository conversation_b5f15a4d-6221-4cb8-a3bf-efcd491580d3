import base64
import json
import asyncio
import time
import os
import websockets
import sqlite3
import hashlib
import hmac
import secrets
import functools
import traceback
from typing import Dict, List, Optional, Set, Any
from dotenv import load_dotenv
from XianyuApis import XianyuApis
import sys
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import re

# 导入日志配置
from loguru import logger

from utils.xianyu_utils import generate_mid, generate_uuid, trans_cookies, generate_device_id, decrypt

# ==================== 内置配置管理 ====================

# ==================== 性能优化组件 ====================

class AIClientManager:
    """AI客户端单例管理器 - 优化性能，避免重复初始化"""
    _instance = None
    _client = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance

    @property
    def client(self):
        """获取AI客户端实例"""
        if self._client is None:
            with self._lock:
                if self._client is None:
                    self._client = self._create_client()
        return self._client

    def _create_client(self):
        """创建AI客户端"""
        try:
            from openai import OpenAI

            # 从配置中获取参数
            api_key = os.getenv("API_KEY", "")
            base_url = os.getenv("BASE_URL", "https://api.deepseek.com/v1")

            if not api_key:
                raise ValueError("API_KEY未配置")

            client = OpenAI(
                api_key=api_key,
                base_url=base_url,
                timeout=30.0,
                max_retries=3
            )

            logger.info("AI客户端初始化成功")
            return client

        except Exception as e:
            logger.error(f"AI客户端初始化失败: {e}")
            raise

    def refresh_client(self):
        """刷新客户端配置"""
        with self._lock:
            self._client = None
            logger.info("AI客户端已刷新")

class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self.metrics = {
            'ai_calls': 0,
            'ai_call_time': 0,
            'db_operations': 0,
            'db_operation_time': 0,
            'memory_usage': 0
        }
        self.start_time = time.time()

    def record_ai_call(self, duration: float):
        """记录AI调用性能"""
        self.metrics['ai_calls'] += 1
        self.metrics['ai_call_time'] += duration

    def record_db_operation(self, duration: float):
        """记录数据库操作性能"""
        self.metrics['db_operations'] += 1
        self.metrics['db_operation_time'] += duration

    def get_stats(self) -> dict:
        """获取性能统计"""
        uptime = time.time() - self.start_time
        return {
            'uptime': uptime,
            'avg_ai_call_time': self.metrics['ai_call_time'] / max(1, self.metrics['ai_calls']),
            'avg_db_operation_time': self.metrics['db_operation_time'] / max(1, self.metrics['db_operations']),
            'ai_calls_per_minute': self.metrics['ai_calls'] / max(1, uptime / 60),
            **self.metrics
        }

# 全局性能监控器
performance_monitor = PerformanceMonitor()

import threading

@dataclass
class DatabaseConfig:
    """数据库配置"""
    path: str = "data/chat_history.db"
    max_history: int = 100
    backup_interval: int = 3600

    def __post_init__(self):
        db_dir = Path(self.path).parent
        db_dir.mkdir(parents=True, exist_ok=True)

@dataclass
class WebSocketConfig:
    """WebSocket配置"""
    base_url: str = "wss://wss-goofish.dingtalk.com/"
    heartbeat_interval: int = 15
    heartbeat_timeout: int = 5
    token_refresh_interval: int = 3600
    token_retry_interval: int = 300
    message_expire_time: int = 300000

@dataclass
class AIConfig:
    """AI配置"""
    api_key: str = ""
    base_url: str = "https://api.deepseek.com/v1"
    model_name: str = "deepseek-chat"
    temperature: float = 0.7
    max_tokens: int = 2000
    top_p: float = 0.8

@dataclass
class SecurityConfig:
    """安全配置"""
    cookies_str: str = ""
    toggle_keywords: str = "。"
    manual_mode_timeout: int = 3600
    max_login_attempts: int = 3
    session_timeout: int = 7200

@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    format: str = "<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    rotation: str = "100 MB"
    retention: str = "30 days"
    file_path: str = "logs/xianyu_agent.log"

class ConfigManager:
    """配置管理器"""

    def __init__(self, strict_validation: bool = True):
        self.strict_validation = strict_validation
        self.env_file = ".env"

        # 初始化默认配置
        self.database = DatabaseConfig()
        self.websocket = WebSocketConfig()
        self.ai = AIConfig()
        self.security = SecurityConfig()
        self.log = LogConfig()

        # 加载配置
        self.load_config()

    def load_config(self) -> None:
        """加载配置"""
        try:
            load_dotenv(self.env_file)

            self.database = DatabaseConfig(
                path=os.getenv("DB_PATH", "data/chat_history.db"),
                max_history=int(os.getenv("MAX_HISTORY", "100")),
                backup_interval=int(os.getenv("BACKUP_INTERVAL", "3600"))
            )

            self.websocket = WebSocketConfig(
                base_url=os.getenv("WEBSOCKET_URL", "wss://wss-goofish.dingtalk.com/"),
                heartbeat_interval=int(os.getenv("HEARTBEAT_INTERVAL", "15")),
                heartbeat_timeout=int(os.getenv("HEARTBEAT_TIMEOUT", "5")),
                token_refresh_interval=int(os.getenv("TOKEN_REFRESH_INTERVAL", "3600")),
                token_retry_interval=int(os.getenv("TOKEN_RETRY_INTERVAL", "300")),
                message_expire_time=int(os.getenv("MESSAGE_EXPIRE_TIME", "300000"))
            )

            self.ai = AIConfig(
                api_key=os.getenv("API_KEY", ""),
                base_url=os.getenv("BASE_URL", "https://api.deepseek.com/v1"),
                model_name=os.getenv("MODEL", "deepseek-chat"),
                temperature=float(os.getenv("TEMPERATURE", "0.7")),
                max_tokens=int(os.getenv("MAX_TOKENS", "2000")),
                top_p=float(os.getenv("AI_TOP_P", "0.8"))
            )

            self.security = SecurityConfig(
                cookies_str=os.getenv("COOKIES_STR", ""),
                toggle_keywords=os.getenv("TOGGLE_KEYWORDS", "。"),
                manual_mode_timeout=int(os.getenv("MANUAL_MODE_TIMEOUT", "3600")),
                max_login_attempts=int(os.getenv("MAX_LOGIN_ATTEMPTS", "3")),
                session_timeout=int(os.getenv("SESSION_TIMEOUT", "7200"))
            )

            self.log = LogConfig(
                level=os.getenv("LOG_LEVEL", "INFO").upper(),
                format=os.getenv("LOG_FORMAT", self.log.format),
                rotation=os.getenv("LOG_ROTATION", "100 MB"),
                retention=os.getenv("LOG_RETENTION", "30 days"),
                file_path=os.getenv("LOG_FILE_PATH", "logs/xianyu_agent.log")
            )

            logger.info("配置加载成功")

        except Exception as e:
            if self.strict_validation:
                logger.error(f"配置加载失败: {e}")
                raise
            else:
                logger.warning(f"配置加载时出现问题: {e}，将使用默认配置")

# 全局配置实例
config = ConfigManager(strict_validation=False)

# ==================== 内置异常处理 ====================

class XianyuAgentException(Exception):
    """闲鱼代理基础异常类"""

    def __init__(self, message: str, error_code: str = "UNKNOWN", details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)

class NetworkError(XianyuAgentException):
    """网络错误"""

    def __init__(self, message: str, status_code: Optional[int] = None, url: Optional[str] = None):
        super().__init__(
            message=message,
            error_code="NETWORK_ERROR",
            details={"status_code": status_code, "url": url}
        )

class WebSocketError(XianyuAgentException):
    """WebSocket错误"""

    def __init__(self, message: str, ws_state: Optional[str] = None):
        super().__init__(
            message=message,
            error_code="WEBSOCKET_ERROR",
            details={"ws_state": ws_state}
        )

def handle_async_exceptions(func):
    """异步函数异常处理装饰器"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"异步函数 {func.__name__} 执行失败: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise
    return wrapper

def unified_error_handler(operation_name: str, default_return=None, log_level="ERROR"):
    """统一错误处理装饰器 - 优化错误处理逻辑"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time

                # 记录性能指标
                if 'ai' in operation_name.lower():
                    performance_monitor.record_ai_call(duration)
                elif 'db' in operation_name.lower() or 'database' in operation_name.lower():
                    performance_monitor.record_db_operation(duration)

                return result
            except Exception as e:
                duration = time.time() - start_time
                error_msg = f"{operation_name}失败 (耗时: {duration:.2f}s): {str(e)}"

                if log_level == "ERROR":
                    logger.error(error_msg)
                elif log_level == "WARNING":
                    logger.warning(error_msg)
                else:
                    logger.info(error_msg)

                return default_return
        return wrapper
    return decorator

def retry_on_failure(max_retries=3, delay=1.0, backoff=2.0, exceptions=(Exception,)):
    """重试装饰器 - 优化错误恢复机制"""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_retries - 1:
                        logger.error(f"{func.__name__} 重试{max_retries}次后仍然失败: {e}")
                        raise

                    wait_time = delay * (backoff ** attempt)
                    logger.warning(f"{func.__name__} 第{attempt + 1}次尝试失败，{wait_time:.1f}秒后重试: {e}")
                    time.sleep(wait_time)

            raise last_exception
        return wrapper
    return decorator

async def async_retry_on_failure(max_retries=3, delay=1.0, backoff=2.0, exceptions=(Exception,)):
    """异步重试装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None

            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt == max_retries - 1:
                        logger.error(f"{func.__name__} 异步重试{max_retries}次后仍然失败: {e}")
                        raise

                    wait_time = delay * (backoff ** attempt)
                    logger.warning(f"{func.__name__} 异步第{attempt + 1}次尝试失败，{wait_time:.1f}秒后重试: {e}")
                    await asyncio.sleep(wait_time)

            raise last_exception
        return wrapper
    return decorator

def error_handler(func):
    """通用错误处理装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            raise
    return wrapper

# ==================== 内置安全管理 ====================

class SecurityManager:
    """安全管理器"""

    def __init__(self):
        self.cookie_encryption = None

    def validate_and_sanitize_input(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """验证和清理输入数据"""
        sanitized = {}
        for key, value in data.items():
            if isinstance(value, str):
                # 基本的XSS防护
                value = re.sub(r'<script.*?</script>', '', value, flags=re.IGNORECASE | re.DOTALL)
                value = re.sub(r'javascript:', '', value, flags=re.IGNORECASE)
            sanitized[key] = value
        return sanitized

    def validate_cookies(self, cookies_str: str) -> bool:
        """验证Cookie格式"""
        if not cookies_str or not isinstance(cookies_str, str):
            return False

        # 检查基本的Cookie格式
        cookie_pattern = r'^[a-zA-Z0-9_\-=;,.\s]+$'
        return bool(re.match(cookie_pattern, cookies_str))

security_manager = SecurityManager()

# ==================== 内置上下文管理 ====================

class ChatContextManager:
    """聊天上下文管理器"""

    def __init__(self, max_history: Optional[int] = None, db_path: Optional[str] = None):
        self.max_history = max_history or config.database.max_history
        self.db_path = db_path or config.database.path
        self._init_db()

    def _init_db(self) -> None:
        """初始化数据库表结构"""
        try:
            db_dir = os.path.dirname(self.db_path)
            if db_dir and not os.path.exists(db_dir):
                os.makedirs(db_dir)

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                CREATE TABLE IF NOT EXISTS messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id TEXT NOT NULL,
                    item_id TEXT NOT NULL,
                    role TEXT NOT NULL,
                    content TEXT NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    session_id TEXT,
                    metadata TEXT
                )
            ''')

            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_user_item ON messages(user_id, item_id);
            ''')

            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_session ON messages(session_id);
            ''')

            conn.commit()
            conn.close()
            logger.info(f"数据库初始化成功: {self.db_path}")

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise

    def add_message(self, user_id: str, item_id: str, role: str, content: str, session_id: Optional[str] = None, metadata: Optional[Dict] = None) -> None:
        """添加消息到上下文"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            metadata_json = json.dumps(metadata) if metadata else None

            cursor.execute('''
                INSERT INTO messages (user_id, item_id, role, content, session_id, metadata)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (user_id, item_id, role, content, session_id, metadata_json))

            conn.commit()
            conn.close()

            # 清理旧消息
            self._cleanup_old_messages(user_id, item_id)

        except Exception as e:
            logger.error(f"添加消息失败: {e}")

    def get_context(self, user_id: str, item_id: str, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取对话上下文"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            limit = limit or self.max_history

            cursor.execute('''
                SELECT role, content, timestamp, session_id, metadata
                FROM messages
                WHERE user_id = ? AND item_id = ?
                ORDER BY timestamp DESC
                LIMIT ?
            ''', (user_id, item_id, limit))

            rows = cursor.fetchall()
            conn.close()

            messages = []
            for row in rows:
                metadata = json.loads(row[4]) if row[4] else {}
                messages.append({
                    "role": row[0],
                    "content": row[1],
                    "timestamp": row[2],
                    "session_id": row[3],
                    "metadata": metadata
                })

            return list(reversed(messages))

        except Exception as e:
            logger.error(f"获取上下文失败: {e}")
            return []

    def _cleanup_old_messages(self, user_id: str, item_id: str) -> None:
        """清理旧消息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('''
                DELETE FROM messages
                WHERE user_id = ? AND item_id = ?
                AND id NOT IN (
                    SELECT id FROM messages
                    WHERE user_id = ? AND item_id = ?
                    ORDER BY timestamp DESC
                    LIMIT ?
                )
            ''', (user_id, item_id, user_id, item_id, self.max_history))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"清理旧消息失败: {e}")

# ==================== 内置日志配置 ====================

def setup_logger():
    """配置日志系统"""
    try:
        logger.remove()

        log_dir = Path(config.log.file_path).parent
        log_dir.mkdir(parents=True, exist_ok=True)

        logger.add(
            sys.stdout,
            format=config.log.format,
            level=config.log.level,
            colorize=True,
            backtrace=True,
            diagnose=True
        )

        logger.add(
            config.log.file_path,
            format=config.log.format,
            level=config.log.level,
            rotation=config.log.rotation,
            retention=config.log.retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )

        # 错误日志单独文件
        error_log_path = config.log.file_path.replace('.log', '.error.log')
        logger.add(
            error_log_path,
            format=config.log.format,
            level="ERROR",
            rotation=config.log.rotation,
            retention=config.log.retention,
            compression="zip",
            backtrace=True,
            diagnose=True,
            encoding="utf-8"
        )

        logger.info("日志系统初始化成功")

    except Exception as e:
        print(f"日志系统初始化失败: {e}")

# 初始化日志系统
setup_logger()

# ==================== 内置智能回复生成器 ====================

class SmartReplyGenerator:
    """智能回复生成器 (性能优化版)"""

    def __init__(self, product_manager):
        self.product_manager = product_manager
        self.config = config

        # 使用单例AI客户端管理器 (性能优化)
        self.ai_client_manager = AIClientManager()

        # 回复缓存 (性能优化)
        self.reply_cache = {}
        self.cache_ttl = 1800  # 30分钟缓存

        # 通用模式提示词
        self.general_system_prompt = """你是一个专业的闲鱼客服助手，需要根据商品信息和用户消息生成合适的回复。

回复要求：
1. 语气友好、自然，像真人对话
2. 根据商品信息准确回答问题
3. 适当推销商品优点，但不要过于夸张
4. 价格相关问题要合理议价，不能无底线降价
5. 回复简洁明了，不要太长

请根据以下信息生成回复："""

        # 针对性模式提示词
        self.specific_system_prompt = """你是一个专业的闲鱼客服助手，需要根据预设的回复策略和模板生成个性化回复。

回复要求：
1. 严格按照预设的回复策略执行
2. 使用提供的回复模板作为参考
3. 保持一致的回复风格和语调
4. 遵守价格底线和议价规则
5. 突出商品的特殊卖点

请根据以下信息生成回复："""

    def generate_reply(self,
                      user_message: str,
                      item_id: str,
                      context: List[Dict] = None,
                      intent: str = "general") -> str:
        """
        生成智能回复

        Args:
            user_message: 用户消息
            item_id: 商品ID
            context: 对话上下文
            intent: 意图类型 (general, price, tech等)

        Returns:
            str: 生成的回复
        """
        try:
            # 获取商品信息
            product = self.product_manager.get_product(item_id)
            if not product:
                return "抱歉，暂时无法获取商品信息，请稍后再试。"

            # 获取回复策略
            strategy = self.product_manager.get_reply_strategy(item_id)

            # 根据模式选择生成方法
            if strategy.mode == "specific":
                return self._generate_specific_reply(user_message, product, strategy, context, intent)
            else:
                return self._generate_general_reply(user_message, product, strategy, context, intent)

        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return "抱歉，我现在有点忙，请稍后再联系我。"

    def _generate_general_reply(self,
                               user_message: str,
                               product,
                               strategy,
                               context: List[Dict],
                               intent: str) -> str:
        """生成通用模式回复"""

        # 自动分析商品信息
        analysis = self.product_manager.analyze_product_automatically(product)

        # 构建提示词
        prompt = self._build_general_prompt(user_message, product, analysis, context, intent)

        # 调用AI生成回复
        messages = [
            {"role": "system", "content": self.general_system_prompt},
            {"role": "user", "content": prompt}
        ]

        return self._call_ai(messages)

    def _generate_specific_reply(self,
                                user_message: str,
                                product,
                                strategy,
                                context: List[Dict],
                                intent: str) -> str:
        """生成针对性模式回复"""

        # 检查是否有预设模板
        template = self._get_template_by_intent(strategy, intent)

        if template:
            # 使用模板生成回复
            return self._generate_from_template(template, user_message, product, strategy, context)
        else:
            # 使用AI生成但遵循策略
            prompt = self._build_specific_prompt(user_message, product, strategy, context, intent)

            messages = [
                {"role": "system", "content": self.specific_system_prompt},
                {"role": "user", "content": prompt}
            ]

            return self._call_ai(messages)

    def _build_general_prompt(self,
                             user_message: str,
                             product,
                             analysis: Dict,
                             context: List[Dict],
                             intent: str) -> str:
        """构建通用模式提示词"""

        prompt_parts = [
            f"商品信息：",
            f"- 标题：{product.title}",
            f"- 价格：{product.price}元",
            f"- 描述：{product.description[:200]}...",
            f"- 状态：{product.stock_status}",
            ""
        ]

        # 添加分析结果
        if analysis.get("selling_points"):
            prompt_parts.append(f"商品卖点：{', '.join(analysis['selling_points'])}")

        if analysis.get("keywords"):
            prompt_parts.append(f"关键词：{', '.join(analysis['keywords'])}")

        # 添加价格分析
        price_analysis = analysis.get("price_analysis", {})
        if price_analysis:
            prompt_parts.append(f"价格分析：{price_analysis}")

        prompt_parts.extend([
            "",
            f"用户消息：{user_message}",
            f"意图类型：{intent}",
            ""
        ])

        # 添加上下文
        if context:
            prompt_parts.append("对话历史：")
            for msg in context[-3:]:  # 只取最近3条
                role = "用户" if msg["role"] == "user" else "客服"
                prompt_parts.append(f"{role}：{msg['content']}")
            prompt_parts.append("")

        prompt_parts.append("请生成一个合适的回复：")

        return "\n".join(prompt_parts)

    def _build_specific_prompt(self,
                              user_message: str,
                              product,
                              strategy,
                              context: List[Dict],
                              intent: str) -> str:
        """构建针对性模式提示词"""

        prompt_parts = [
            f"商品信息：",
            f"- 标题：{product.title}",
            f"- 价格：{product.price}元",
            f"- 描述：{product.description[:200]}...",
            "",
            f"回复策略：",
            f"- 最低价格：{strategy.min_price}元",
            f"- 最大折扣：{strategy.max_discount_percent}%",
            f"- 议价轮数：{strategy.bargain_rounds}",
            ""
        ]

        # 添加特殊说明
        if strategy.special_notes:
            prompt_parts.append(f"特殊说明：{strategy.special_notes}")

        # 添加卖点
        if strategy.selling_points:
            prompt_parts.append(f"重点卖点：{', '.join(strategy.selling_points)}")

        prompt_parts.extend([
            "",
            f"用户消息：{user_message}",
            f"意图类型：{intent}",
            ""
        ])

        # 添加上下文
        if context:
            prompt_parts.append("对话历史：")
            for msg in context[-3:]:
                role = "用户" if msg["role"] == "user" else "客服"
                prompt_parts.append(f"{role}：{msg['content']}")
            prompt_parts.append("")

        prompt_parts.append("请根据策略生成回复：")

        return "\n".join(prompt_parts)

    def _get_template_by_intent(self, strategy, intent: str) -> Optional[str]:
        """根据意图获取对应模板"""
        template_map = {
            "general": strategy.greeting_template,
            "price": strategy.price_response_template,
            "tech": strategy.tech_response_template,
            "final": strategy.final_price_template
        }

        template = template_map.get(intent, "")
        return template if template.strip() else None

    def _generate_from_template(self,
                               template: str,
                               user_message: str,
                               product,
                               strategy,
                               context: List[Dict]) -> str:
        """从模板生成回复"""

        # 模板变量替换
        variables = {
            "{product_title}": product.title,
            "{product_price}": str(product.price),
            "{min_price}": str(strategy.min_price),
            "{user_message}": user_message,
            "{special_notes}": strategy.special_notes
        }

        reply = template
        for var, value in variables.items():
            reply = reply.replace(var, value)

        return reply

    @unified_error_handler("AI调用", "抱歉，我现在有点忙，请稍后再联系我。")
    @retry_on_failure(max_retries=3, delay=1.0, exceptions=(Exception,))
    def _call_ai(self, messages: List[Dict]) -> str:
        """调用AI生成回复 (性能优化版)"""
        # 生成缓存键
        cache_key = self._generate_cache_key(messages)

        # 检查缓存
        cached_reply = self._get_cached_reply(cache_key)
        if cached_reply:
            logger.debug("使用缓存回复")
            return cached_reply

        start_time = time.time()
        try:
            # 使用单例客户端
            client = self.ai_client_manager.client

            response = client.chat.completions.create(
                model=self.config.ai.model_name,
                messages=messages,
                temperature=self.config.ai.temperature,
                max_tokens=self.config.ai.max_tokens,
                top_p=self.config.ai.top_p
            )

            reply = response.choices[0].message.content.strip()

            # 缓存回复
            self._cache_reply(cache_key, reply)

            # 记录性能指标
            duration = time.time() - start_time
            performance_monitor.record_ai_call(duration)

            logger.debug(f"AI调用成功，耗时: {duration:.2f}s")
            return reply

        except Exception as e:
            logger.error(f"AI调用失败: {e}")
            raise

    def _generate_cache_key(self, messages: List[Dict]) -> str:
        """生成缓存键"""
        import hashlib
        content = json.dumps(messages, sort_keys=True, ensure_ascii=False)
        return hashlib.md5(content.encode()).hexdigest()

    def _get_cached_reply(self, cache_key: str) -> Optional[str]:
        """获取缓存回复"""
        if cache_key in self.reply_cache:
            reply, timestamp = self.reply_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                return reply
            else:
                # 清理过期缓存
                del self.reply_cache[cache_key]
        return None

    def _cache_reply(self, cache_key: str, reply: str):
        """缓存回复"""
        self.reply_cache[cache_key] = (reply, time.time())

        # 限制缓存大小
        if len(self.reply_cache) > 1000:
            # 清理最旧的缓存项
            oldest_key = min(self.reply_cache.keys(),
                           key=lambda k: self.reply_cache[k][1])
            del self.reply_cache[oldest_key]

    def calculate_bargain_price(self,
                               product,
                               strategy,
                               bargain_round: int) -> float:
        """计算议价价格"""

        # 基础价格
        base_price = product.price

        # 最低价格
        min_price = max(strategy.min_price, base_price * (1 - strategy.max_discount_percent / 100))

        # 根据议价轮数计算折扣
        if bargain_round >= strategy.bargain_rounds:
            # 达到最大轮数，给出最低价
            return min_price
        else:
            # 渐进式降价
            discount_step = strategy.max_discount_percent / strategy.bargain_rounds
            current_discount = discount_step * bargain_round
            suggested_price = base_price * (1 - current_discount / 100)

            return max(suggested_price, min_price)

    def is_price_acceptable(self,
                           product,
                           strategy,
                           offered_price: float) -> bool:
        """判断用户出价是否可接受"""
        min_price = max(strategy.min_price, product.price * (1 - strategy.max_discount_percent / 100))
        return offered_price >= min_price

# ==================== 内置传统回复机器人 ====================

class XianyuReplyBot:
    """闲鱼智能回复机器人"""

    def __init__(self):
        """初始化回复机器人 (性能优化版)"""
        try:
            # 验证AI配置
            if not config.ai.api_key:
                raise ConfigurationError("API_KEY未配置")

            # 使用单例AI客户端管理器 (性能优化)
            self.ai_client_manager = AIClientManager()

            self._init_system_prompts()
            self._init_agents()
            self.router = IntentRouter(self.agents['classify'])
            self.last_intent: Optional[str] = None  # 记录最后一次意图

            logger.info("XianyuReplyBot初始化完成 (性能优化版)")

        except Exception as e:
            logger.error(f"XianyuReplyBot初始化失败: {e}")
            raise

    def _init_agents(self) -> None:
        """初始化各领域Agent (性能优化版)"""
        try:
            self.agents: Dict[str, 'BaseAgent'] = {
                'classify': ClassifyAgent(self.classify_prompt, self._safe_filter),
                'price': PriceAgent(self.price_prompt, self._safe_filter),
                'tech': TechAgent(self.tech_prompt, self._safe_filter),
                'default': DefaultAgent(self.default_prompt, self._safe_filter),
            }
            logger.debug("Agent初始化完成 (性能优化版)")
        except Exception as e:
            logger.error(f"Agent初始化失败: {e}")
            raise

    def _init_system_prompts(self) -> None:
        """初始化各Agent专用提示词，直接从文件中加载"""
        prompt_dir = "prompts"

        try:
            # 加载分类提示词
            classify_path = os.path.join(prompt_dir, "classify_prompt.txt")
            if not os.path.exists(classify_path):
                raise FileNotFoundError(f"分类提示词文件不存在: {classify_path}")

            with open(classify_path, "r", encoding="utf-8") as f:
                self.classify_prompt = f.read().strip()
                logger.debug(f"已加载分类提示词，长度: {len(self.classify_prompt)} 字符")

            # 加载价格提示词
            price_path = os.path.join(prompt_dir, "price_prompt.txt")
            if not os.path.exists(price_path):
                raise FileNotFoundError(f"价格提示词文件不存在: {price_path}")

            with open(price_path, "r", encoding="utf-8") as f:
                self.price_prompt = f.read().strip()
                logger.debug(f"已加载价格提示词，长度: {len(self.price_prompt)} 字符")

            # 加载技术提示词
            tech_path = os.path.join(prompt_dir, "tech_prompt.txt")
            if not os.path.exists(tech_path):
                raise FileNotFoundError(f"技术提示词文件不存在: {tech_path}")

            with open(tech_path, "r", encoding="utf-8") as f:
                self.tech_prompt = f.read().strip()
                logger.debug(f"已加载技术提示词，长度: {len(self.tech_prompt)} 字符")

            # 加载默认提示词
            default_path = os.path.join(prompt_dir, "default_prompt.txt")
            if not os.path.exists(default_path):
                raise FileNotFoundError(f"默认提示词文件不存在: {default_path}")

            with open(default_path, "r", encoding="utf-8") as f:
                self.default_prompt = f.read().strip()
                logger.debug(f"已加载默认提示词，长度: {len(self.default_prompt)} 字符")

            logger.info("成功加载所有提示词")

        except Exception as e:
            logger.error(f"提示词加载失败: {e}")
            raise

    def _safe_filter(self, text: str) -> str:
        """安全过滤模块"""
        blocked_phrases = ["微信", "QQ", "支付宝", "银行卡", "线下"]
        return "[安全提醒]请通过平台沟通" if any(p in text for p in blocked_phrases) else text

    def format_history(self, context: List[Dict]) -> str:
        """格式化对话历史，返回完整的对话记录"""
        # 过滤掉系统消息，只保留用户和助手的对话
        user_assistant_msgs = [msg for msg in context if msg['role'] in ['user', 'assistant']]
        return "\n".join([f"{msg['role']}: {msg['content']}" for msg in user_assistant_msgs])

    def generate_reply(self, user_msg: str, item_desc: str, context: List[Dict]) -> str:
        """生成回复主流程"""
        # 记录用户消息
        # logger.debug(f'用户所发消息: {user_msg}')

        formatted_context = self.format_history(context)
        # logger.debug(f'对话历史: {formatted_context}')

        # 1. 路由决策
        detected_intent = self.router.detect(user_msg, item_desc, formatted_context)

        # 2. 获取对应Agent
        internal_intents = {'classify'}  # 定义不对外开放的Agent

        if detected_intent in self.agents and detected_intent not in internal_intents:
            agent = self.agents[detected_intent]
            logger.info(f'意图识别完成: {detected_intent}')
            self.last_intent = detected_intent  # 保存当前意图
        else:
            agent = self.agents['default']
            logger.info(f'意图识别完成: default')
            self.last_intent = 'default'  # 保存当前意图

        # 3. 获取议价次数
        bargain_count = self._extract_bargain_count(context)
        logger.info(f'议价次数: {bargain_count}')

        # 4. 生成回复
        return agent.generate(
            user_msg=user_msg,
            item_desc=item_desc,
            context=formatted_context,
            bargain_count=bargain_count
        )

    def _extract_bargain_count(self, context: List[Dict]) -> int:
        """
        从上下文中提取议价次数信息

        Args:
            context: 对话历史

        Returns:
            int: 议价次数，如果没有找到则返回0
        """
        # 查找系统消息中的议价次数信息
        for msg in context:
            if msg['role'] == 'system' and '议价次数' in msg['content']:
                try:
                    # 提取议价次数
                    match = re.search(r'议价次数[:：]\s*(\d+)', msg['content'])
                    if match:
                        return int(match.group(1))
                except Exception:
                    pass
        return 0

    def reload_prompts(self):
        """重新加载所有提示词"""
        logger.info("正在重新加载提示词...")
        self._init_system_prompts()
        self._init_agents()
        logger.info("提示词重新加载完成")


class IntentRouter:
    """意图路由决策器"""

    def __init__(self, classify_agent):
        self.rules = {
            'tech': {  # 技术类优先判定
                'keywords': ['参数', '规格', '型号', '连接', '对比'],
                'patterns': [
                    r'和.+比'
                ]
            },
            'price': {
                'keywords': ['便宜', '价', '砍价', '少点'],
                'patterns': [r'\d+元', r'能少\d+']
            }
        }
        self.classify_agent = classify_agent

    def detect(self, user_msg: str, item_desc, context) -> str:
        """三级路由策略（技术优先）"""
        text_clean = re.sub(r'[^\w\u4e00-\u9fa5]', '', user_msg)

        # 1. 技术类关键词优先检查
        if any(kw in text_clean for kw in self.rules['tech']['keywords']):
            # logger.debug(f"技术类关键词匹配: {[kw for kw in self.rules['tech']['keywords'] if kw in text_clean]}")
            return 'tech'

        # 2. 技术类正则优先检查
        for pattern in self.rules['tech']['patterns']:
            if re.search(pattern, text_clean):
                # logger.debug(f"技术类正则匹配: {pattern}")
                return 'tech'

        # 3. 价格类检查
        for intent in ['price']:
            if any(kw in text_clean for kw in self.rules[intent]['keywords']):
                # logger.debug(f"价格类关键词匹配: {[kw for kw in self.rules[intent]['keywords'] if kw in text_clean]}")
                return intent

            for pattern in self.rules[intent]['patterns']:
                if re.search(pattern, text_clean):
                    # logger.debug(f"价格类正则匹配: {pattern}")
                    return intent

        # 4. 大模型兜底
        # logger.debug("使用大模型进行意图分类")
        return self.classify_agent.generate(
            user_msg=user_msg,
            item_desc=item_desc,
            context=context
        )


class BaseAgent:
    """Agent基类 (性能优化版)"""

    def __init__(self, system_prompt, safety_filter):
        # 使用单例AI客户端管理器 (性能优化)
        self.ai_client_manager = AIClientManager()
        self.system_prompt = system_prompt
        self.safety_filter = safety_filter

        # Agent级别的缓存 (性能优化)
        self.agent_cache = {}
        self.cache_ttl = 900  # 15分钟缓存

    @unified_error_handler("Agent回复生成", "抱歉，请稍后再试。")
    def generate(self, user_msg: str, item_desc: str, context: str, bargain_count: int = 0) -> str:
        """生成回复模板方法 (性能优化版)"""
        # 检查缓存
        cache_key = self._generate_agent_cache_key(user_msg, item_desc, context)
        cached_response = self._get_agent_cached_response(cache_key)
        if cached_response:
            logger.debug(f"{self.__class__.__name__} 使用缓存回复")
            return cached_response

        messages = self._build_messages(user_msg, item_desc, context)
        response = self._call_llm(messages)
        filtered_response = self.safety_filter(response)

        # 缓存回复
        self._cache_agent_response(cache_key, filtered_response)

        return filtered_response

    def _build_messages(self, user_msg: str, item_desc: str, context: str) -> List[Dict]:
        """构建消息链"""
        return [
            {"role": "system", "content": f"【商品信息】{item_desc}\n【你与客户对话历史】{context}\n{self.system_prompt}"},
            {"role": "user", "content": user_msg}
        ]

    @retry_on_failure(max_retries=2, delay=0.5)
    def _call_llm(self, messages: List[Dict], temperature: float = 0.4) -> str:
        """调用大模型 (性能优化版)"""
        start_time = time.time()

        client = self.ai_client_manager.client
        response = client.chat.completions.create(
            model=config.ai.model_name,
            messages=messages,
            temperature=temperature,
            max_tokens=config.ai.max_tokens,
            top_p=config.ai.top_p
        )

        # 记录性能指标
        duration = time.time() - start_time
        performance_monitor.record_ai_call(duration)

        return response.choices[0].message.content

    def _generate_agent_cache_key(self, user_msg: str, item_desc: str, context: str) -> str:
        """生成Agent缓存键"""
        import hashlib
        content = f"{self.__class__.__name__}:{user_msg}:{item_desc}:{context}"
        return hashlib.md5(content.encode()).hexdigest()

    def _get_agent_cached_response(self, cache_key: str) -> Optional[str]:
        """获取Agent缓存回复"""
        if cache_key in self.agent_cache:
            response, timestamp = self.agent_cache[cache_key]
            if time.time() - timestamp < self.cache_ttl:
                return response
            else:
                del self.agent_cache[cache_key]
        return None

    def _cache_agent_response(self, cache_key: str, response: str):
        """缓存Agent回复"""
        self.agent_cache[cache_key] = (response, time.time())

        # 限制缓存大小
        if len(self.agent_cache) > 500:
            oldest_key = min(self.agent_cache.keys(),
                           key=lambda k: self.agent_cache[k][1])
            del self.agent_cache[oldest_key]


class PriceAgent(BaseAgent):
    """议价处理Agent"""

    def generate(self, user_msg: str, item_desc: str, context: str, bargain_count: int=0) -> str:
        """重写生成逻辑 (性能优化版)"""
        # 检查缓存 (包含议价轮次)
        cache_key = self._generate_price_cache_key(user_msg, item_desc, context, bargain_count)
        cached_response = self._get_agent_cached_response(cache_key)
        if cached_response:
            logger.debug("PriceAgent 使用缓存回复")
            return cached_response

        dynamic_temp = self._calc_temperature(bargain_count)
        messages = self._build_messages(user_msg, item_desc, context)
        messages[0]['content'] += f"\n▲当前议价轮次：{bargain_count}"

        response = self._call_llm(messages, temperature=dynamic_temp)
        filtered_response = self.safety_filter(response)

        # 缓存回复
        self._cache_agent_response(cache_key, filtered_response)

        return filtered_response

    def _generate_price_cache_key(self, user_msg: str, item_desc: str, context: str, bargain_count: int) -> str:
        """生成价格Agent专用缓存键"""
        import hashlib
        content = f"PriceAgent:{user_msg}:{item_desc}:{context}:{bargain_count}"
        return hashlib.md5(content.encode()).hexdigest()

    def _calc_temperature(self, bargain_count: int) -> float:
        """动态温度策略"""
        return min(0.3 + bargain_count * 0.15, 0.9)


class TechAgent(BaseAgent):
    """技术咨询Agent"""
    def generate(self, user_msg: str, item_desc: str, context: str, bargain_count: int=0) -> str:
        """重写生成逻辑"""
        messages = self._build_messages(user_msg, item_desc, context)
        # messages[0]['content'] += "\n▲知识库：\n" + self._fetch_tech_specs()

        response = self.client.chat.completions.create(
            model=config.ai.model_name,
            messages=messages,
            temperature=0.4,
            max_tokens=config.ai.max_tokens,
            top_p=config.ai.top_p,
            extra_body={
                "enable_search": True,
            }
        )

        return self.safety_filter(response.choices[0].message.content)


class ClassifyAgent(BaseAgent):
    """意图识别Agent"""

    def generate(self, **args) -> str:
        response = super().generate(**args)
        return response


class DefaultAgent(BaseAgent):
    """默认处理Agent"""

    def _call_llm(self, messages: List[Dict], *args) -> str:
        """限制默认回复长度"""
        response = super()._call_llm(messages, temperature=0.7)
        return response

# ==================== 内置智能闲鱼代理 ====================

class SmartXianyuAgent:
    """智能闲鱼代理 - 集成智能商品管理功能"""

    def __init__(self):
        """初始化智能代理"""
        try:
            # 导入智能商品管理器
            from smart_product_manager import SmartProductManager

            # 初始化智能商品管理器
            self.product_manager = SmartProductManager()

            # 初始化智能回复生成器
            self.smart_reply_generator = SmartReplyGenerator(self.product_manager)

            # 初始化传统回复机器人（作为备用）
            self.traditional_bot = XianyuReplyBot()

            # 全局模式设置
            self.global_mode = "smart"  # smart(智能模式) 或 traditional(传统模式)

            logger.info("SmartXianyuAgent初始化完成")

        except Exception as e:
            logger.error(f"SmartXianyuAgent初始化失败: {e}")
            raise

    def generate_reply(self, user_msg: str, item_id: str, item_desc: str, context: List[Dict]) -> str:
        """
        生成智能回复

        Args:
            user_msg: 用户消息
            item_id: 商品ID
            item_desc: 商品描述（传统格式）
            context: 对话上下文

        Returns:
            str: 生成的回复
        """
        try:
            # 检查是否启用智能模式
            if self.global_mode == "smart":
                return self._generate_smart_reply(user_msg, item_id, item_desc, context)
            else:
                # 使用传统模式
                return self.traditional_bot.generate_reply(user_msg, item_desc, context)

        except Exception as e:
            logger.error(f"生成回复失败，回退到传统模式: {e}")
            # 出错时回退到传统模式
            return self.traditional_bot.generate_reply(user_msg, item_desc, context)

    def _generate_smart_reply(self, user_msg: str, item_id: str, item_desc: str, context: List[Dict]) -> str:
        """生成智能回复"""

        # 1. 确保商品信息存在
        product = self.product_manager.get_product(item_id)
        if not product:
            # 尝试从传统描述中解析商品信息
            product = self._parse_product_from_desc(item_id, item_desc)
            if product:
                # 保存到数据库
                self.product_manager.save_product(product)
                logger.info(f"自动创建商品信息: {item_id}")

        # 2. 检测意图
        intent = self._detect_intent(user_msg, product, context)

        # 3. 获取回复策略
        strategy = self.product_manager.get_reply_strategy(item_id)

        # 4. 根据策略模式选择生成方法
        if strategy.mode == "specific" and strategy.auto_reply_enabled:
            # 针对性模式
            return self.smart_reply_generator.generate_reply(user_msg, item_id, context, intent)
        elif strategy.mode == "general":
            # 通用模式
            return self.smart_reply_generator.generate_reply(user_msg, item_id, context, intent)
        else:
            # 回退到传统模式
            return self.traditional_bot.generate_reply(user_msg, item_desc, context)

    def _parse_product_from_desc(self, item_id: str, item_desc: str):
        """从传统商品描述中解析商品信息"""
        try:
            # 导入ProductInfo
            from smart_product_manager import ProductInfo

            # 解析价格信息
            price_match = re.search(r'当前商品售卖价格为[:：](\d+(?:\.\d+)?)', item_desc)
            price = float(price_match.group(1)) if price_match else 0.0

            # 解析标题（取描述的前半部分作为标题）
            desc_parts = item_desc.split(';')
            title = desc_parts[0] if desc_parts else item_desc[:50]

            # 创建商品信息对象
            product = ProductInfo(
                item_id=item_id,
                title=title,
                price=price,
                description=item_desc,
                stock_status="available"
            )

            return product

        except Exception as e:
            logger.error(f"解析商品信息失败: {e}")
            return None

    def _detect_intent(self, user_msg: str, product, context: List[Dict]) -> str:
        """检测用户意图"""
        try:
            # 使用传统的意图路由器
            router = self.traditional_bot.router

            # 构建商品描述
            item_desc = ""
            if product:
                item_desc = f"{product.title};当前商品售卖价格为:{product.price}"

            # 格式化上下文
            formatted_context = self.traditional_bot.format_history(context)

            # 检测意图
            intent = router.detect(user_msg, item_desc, formatted_context)

            # 记录最后意图
            self.traditional_bot.last_intent = intent

            return intent

        except Exception as e:
            logger.error(f"意图检测失败: {e}")
            return "general"

    def update_product_from_api(self, item_id: str, api_data: Dict) -> bool:
        """从API数据更新商品信息"""
        try:
            # 提取商品信息
            product = self.product_manager.extract_product_info(api_data)

            # 保存到数据库
            success = self.product_manager.save_product(product)

            if success:
                # 自动分析商品并生成建议策略
                analysis = self.product_manager.analyze_product_automatically(product)

                # 检查是否需要创建默认策略
                existing_strategy = self.product_manager.get_reply_strategy(item_id)
                if existing_strategy.mode == "general" and not existing_strategy.greeting_template:
                    # 导入ReplyStrategy
                    from smart_product_manager import ReplyStrategy

                    # 创建智能推荐的策略
                    recommended = analysis.get("recommended_strategy", {})
                    auto_templates = recommended.get("auto_templates", {})

                    new_strategy = ReplyStrategy(
                        item_id=item_id,
                        mode="general",
                        max_discount_percent=recommended.get("max_discount_percent", 10.0),
                        bargain_rounds=recommended.get("bargain_rounds", 3),
                        greeting_template=auto_templates.get("greeting", ""),
                        price_response_template=auto_templates.get("price", ""),
                        selling_points=analysis.get("selling_points", [])
                    )

                    self.product_manager.save_reply_strategy(new_strategy)
                    logger.info(f"自动创建智能策略: {item_id}")

            return success

        except Exception as e:
            logger.error(f"更新商品信息失败: {e}")
            return False

    def set_global_mode(self, mode: str):
        """设置全局模式"""
        if mode in ["smart", "traditional"]:
            self.global_mode = mode
            logger.info(f"全局模式已切换为: {mode}")
        else:
            logger.warning(f"无效的模式: {mode}")

    def get_product_info(self, item_id: str):
        """获取商品信息"""
        return self.product_manager.get_product(item_id)

    def get_reply_strategy(self, item_id: str):
        """获取回复策略"""
        return self.product_manager.get_reply_strategy(item_id)

    def calculate_bargain_price(self, item_id: str, bargain_round: int):
        """计算议价价格"""
        try:
            product = self.product_manager.get_product(item_id)
            strategy = self.product_manager.get_reply_strategy(item_id)

            if product and strategy:
                return self.smart_reply_generator.calculate_bargain_price(product, strategy, bargain_round)

            return None

        except Exception as e:
            logger.error(f"计算议价价格失败: {e}")
            return None

    def is_price_acceptable(self, item_id: str, offered_price: float) -> bool:
        """判断用户出价是否可接受"""
        try:
            product = self.product_manager.get_product(item_id)
            strategy = self.product_manager.get_reply_strategy(item_id)

            if product and strategy:
                return self.smart_reply_generator.is_price_acceptable(product, strategy, offered_price)

            return False

        except Exception as e:
            logger.error(f"判断价格可接受性失败: {e}")
            return False

    def get_product_analysis(self, item_id: str):
        """获取商品分析结果"""
        try:
            product = self.product_manager.get_product(item_id)
            if product:
                return self.product_manager.analyze_product_automatically(product)
            return None

        except Exception as e:
            logger.error(f"获取商品分析失败: {e}")
            return None

    @property
    def last_intent(self):
        """获取最后检测的意图"""
        return self.traditional_bot.last_intent

    def reload_prompts(self):
        """重新加载提示词"""
        self.traditional_bot.reload_prompts()
        logger.info("提示词重新加载完成")

# ==================== 主程序类 ====================

# 全局变量
bot = None

class XianyuLive:
    """闲鱼实时消息处理类"""

    def __init__(self, cookies_str: str):
        """
        初始化闲鱼实时消息处理器

        Args:
            cookies_str: Cookie字符串
        """
        try:
            # 验证和清理输入
            validated_data = security_manager.validate_and_sanitize_input({
                "cookies": cookies_str
            })

            self.xianyu = XianyuApis()
            self.base_url = config.websocket.base_url
            self.cookies_str = cookies_str
            self.cookies = trans_cookies(cookies_str)
            self.xianyu.session.cookies.update(self.cookies)

            # 验证必要的Cookie字段
            if 'unb' not in self.cookies:
                raise ValueError("Cookie中缺少必要的unb字段")

            self.myid = self.cookies['unb']
            self.device_id = generate_device_id(self.myid)
            self.context_manager = ChatContextManager()

            # 使用配置管理器的配置
            self.heartbeat_interval = config.websocket.heartbeat_interval
            self.heartbeat_timeout = config.websocket.heartbeat_timeout
            self.token_refresh_interval = config.websocket.token_refresh_interval
            self.token_retry_interval = config.websocket.token_retry_interval
            self.message_expire_time = config.websocket.message_expire_time
            self.manual_mode_timeout = config.security.manual_mode_timeout
            self.toggle_keywords = config.security.toggle_keywords

            # 运行时状态
            self.last_heartbeat_time: float = 0
            self.last_heartbeat_response: float = 0
            self.last_token_refresh_time: float = 0
            self.current_token: Optional[str] = None
            self.connection_restart_flag: bool = False

            # 异步任务
            self.heartbeat_task: Optional[asyncio.Task] = None
            self.token_refresh_task: Optional[asyncio.Task] = None
            self.ws: Optional[websockets.WebSocketServerProtocol] = None

            # 人工接管相关
            self.manual_mode_conversations: Set[str] = set()
            self.manual_mode_timestamps: Dict[str, float] = {}

            logger.info(f"XianyuLive初始化完成，用户ID: {self.myid}")

        except Exception as e:
            error_handler.handle_config_error(e, "XianyuLive初始化")
            raise

    @handle_async_exceptions
    async def refresh_token(self) -> Optional[str]:
        """
        刷新访问令牌

        Returns:
            新的访问令牌，失败时返回None
        """
        logger.info("开始刷新token...")

        try:
            # 获取新token（如果Cookie失效，get_token会直接退出程序）
            token_result = self.xianyu.get_token(self.device_id)

            if isinstance(token_result, dict) and 'data' in token_result and 'accessToken' in token_result['data']:
                new_token = token_result['data']['accessToken']
                self.current_token = new_token
                self.last_token_refresh_time = time.time()
                logger.info("Token刷新成功")
                return new_token
            else:
                logger.error(f"Token刷新失败: {token_result}")
                return None

        except Exception as e:
            error_handler.handle_network_error(e, operation="Token刷新")
            return None

    async def token_refresh_loop(self):
        """Token刷新循环"""
        while True:
            try:
                current_time = time.time()
                
                # 检查是否需要刷新token
                if current_time - self.last_token_refresh_time >= self.token_refresh_interval:
                    logger.info("Token即将过期，准备刷新...")
                    
                    new_token = await self.refresh_token()
                    if new_token:
                        logger.info("Token刷新成功，准备重新建立连接...")
                        # 设置连接重启标志
                        self.connection_restart_flag = True
                        # 关闭当前WebSocket连接，触发重连
                        if self.ws:
                            await self.ws.close()
                        break
                    else:
                        logger.error("Token刷新失败，将在{}分钟后重试".format(self.token_retry_interval // 60))
                        await asyncio.sleep(self.token_retry_interval)  # 使用配置的重试间隔
                        continue
                
                # 每分钟检查一次
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"Token刷新循环出错: {e}")
                await asyncio.sleep(60)

    async def send_msg(self, ws, cid, toid, text):
        text = {
            "contentType": 1,
            "text": {
                "text": text
            }
        }
        text_base64 = str(base64.b64encode(json.dumps(text).encode('utf-8')), 'utf-8')
        msg = {
            "lwp": "/r/MessageSend/sendByReceiverScope",
            "headers": {
                "mid": generate_mid()
            },
            "body": [
                {
                    "uuid": generate_uuid(),
                    "cid": f"{cid}@goofish",
                    "conversationType": 1,
                    "content": {
                        "contentType": 101,
                        "custom": {
                            "type": 1,
                            "data": text_base64
                        }
                    },
                    "redPointPolicy": 0,
                    "extension": {
                        "extJson": "{}"
                    },
                    "ctx": {
                        "appVersion": "1.0",
                        "platform": "web"
                    },
                    "mtags": {},
                    "msgReadStatusSetting": 1
                },
                {
                    "actualReceivers": [
                        f"{toid}@goofish",
                        f"{self.myid}@goofish"
                    ]
                }
            ]
        }
        await ws.send(json.dumps(msg))

    async def init(self, ws):
        # 如果没有token或者token过期，获取新token
        if not self.current_token or (time.time() - self.last_token_refresh_time) >= self.token_refresh_interval:
            logger.info("获取初始token...")
            await self.refresh_token()
        
        if not self.current_token:
            logger.error("无法获取有效token，初始化失败")
            raise Exception("Token获取失败")
            
        msg = {
            "lwp": "/reg",
            "headers": {
                "cache-header": "app-key token ua wv",
                "app-key": "444e9908a51d1cb236a27862abc769c9",
                "token": self.current_token,
                "ua": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 DingTalk(2.1.5) OS(Windows/10) Browser(Chrome/*********) DingWeb/2.1.5 IMPaaS DingWeb/2.1.5",
                "dt": "j",
                "wv": "im:3,au:3,sy:6",
                "sync": "0,0;0;0;",
                "did": self.device_id,
                "mid": generate_mid()
            }
        }
        await ws.send(json.dumps(msg))
        # 等待一段时间，确保连接注册完成
        await asyncio.sleep(1)
        msg = {"lwp": "/r/SyncStatus/ackDiff", "headers": {"mid": "5701741704675979 0"}, "body": [
            {"pipeline": "sync", "tooLong2Tag": "PNM,1", "channel": "sync", "topic": "sync", "highPts": 0,
             "pts": int(time.time() * 1000) * 1000, "seq": 0, "timestamp": int(time.time() * 1000)}]}
        await ws.send(json.dumps(msg))
        logger.info('连接注册完成')

    def is_chat_message(self, message):
        """判断是否为用户聊天消息"""
        try:
            return (
                isinstance(message, dict) 
                and "1" in message 
                and isinstance(message["1"], dict)  # 确保是字典类型
                and "10" in message["1"]
                and isinstance(message["1"]["10"], dict)  # 确保是字典类型
                and "reminderContent" in message["1"]["10"]
            )
        except Exception:
            return False

    def is_sync_package(self, message_data):
        """判断是否为同步包消息"""
        try:
            return (
                isinstance(message_data, dict)
                and "body" in message_data
                and "syncPushPackage" in message_data["body"]
                and "data" in message_data["body"]["syncPushPackage"]
                and len(message_data["body"]["syncPushPackage"]["data"]) > 0
            )
        except Exception:
            return False

    def is_typing_status(self, message):
        """判断是否为用户正在输入状态消息"""
        try:
            return (
                isinstance(message, dict)
                and "1" in message
                and isinstance(message["1"], list)
                and len(message["1"]) > 0
                and isinstance(message["1"][0], dict)
                and "1" in message["1"][0]
                and isinstance(message["1"][0]["1"], str)
                and "@goofish" in message["1"][0]["1"]
            )
        except Exception:
            return False

    def is_system_message(self, message):
        """判断是否为系统消息"""
        try:
            return (
                isinstance(message, dict)
                and "3" in message
                and isinstance(message["3"], dict)
                and "needPush" in message["3"]
                and message["3"]["needPush"] == "false"
            )
        except Exception:
            return False

    def check_toggle_keywords(self, message):
        """检查消息是否包含切换关键词"""
        message_stripped = message.strip()
        return message_stripped in self.toggle_keywords

    def is_manual_mode(self, chat_id):
        """检查特定会话是否处于人工接管模式"""
        if chat_id not in self.manual_mode_conversations:
            return False
        
        # 检查是否超时
        current_time = time.time()
        if chat_id in self.manual_mode_timestamps:
            if current_time - self.manual_mode_timestamps[chat_id] > self.manual_mode_timeout:
                # 超时，自动退出人工模式
                self.exit_manual_mode(chat_id)
                return False
        
        return True

    def enter_manual_mode(self, chat_id):
        """进入人工接管模式"""
        self.manual_mode_conversations.add(chat_id)
        self.manual_mode_timestamps[chat_id] = time.time()

    def exit_manual_mode(self, chat_id):
        """退出人工接管模式"""
        self.manual_mode_conversations.discard(chat_id)
        if chat_id in self.manual_mode_timestamps:
            del self.manual_mode_timestamps[chat_id]

    def toggle_manual_mode(self, chat_id):
        """切换人工接管模式"""
        if self.is_manual_mode(chat_id):
            self.exit_manual_mode(chat_id)
            return "auto"
        else:
            self.enter_manual_mode(chat_id)
            return "manual"

    async def handle_message(self, message_data, websocket):
        """处理所有类型的消息"""
        try:

            try:
                message = message_data
                ack = {
                    "code": 200,
                    "headers": {
                        "mid": message["headers"]["mid"] if "mid" in message["headers"] else generate_mid(),
                        "sid": message["headers"]["sid"] if "sid" in message["headers"] else '',
                    }
                }
                if 'app-key' in message["headers"]:
                    ack["headers"]["app-key"] = message["headers"]["app-key"]
                if 'ua' in message["headers"]:
                    ack["headers"]["ua"] = message["headers"]["ua"]
                if 'dt' in message["headers"]:
                    ack["headers"]["dt"] = message["headers"]["dt"]
                await websocket.send(json.dumps(ack))
            except Exception as e:
                pass

            # 如果不是同步包消息，直接返回
            if not self.is_sync_package(message_data):
                return

            # 获取并解密数据
            sync_data = message_data["body"]["syncPushPackage"]["data"][0]
            
            # 检查是否有必要的字段
            if "data" not in sync_data:
                logger.debug("同步包中无data字段")
                return

            # 解密数据
            try:
                data = sync_data["data"]
                try:
                    data = base64.b64decode(data).decode("utf-8")
                    data = json.loads(data)
                    # logger.info(f"无需解密 message: {data}")
                    return
                except Exception as e:
                    # logger.info(f'加密数据: {data}')
                    decrypted_data = decrypt(data)
                    message = json.loads(decrypted_data)
            except Exception as e:
                logger.error(f"消息解密失败: {e}")
                return

            try:
                # 判断是否为订单消息,需要自行编写付款后的逻辑
                if message['3']['redReminder'] == '等待买家付款':
                    user_id = message['1'].split('@')[0]
                    user_url = f'https://www.goofish.com/personal?userId={user_id}'
                    logger.info(f'等待买家 {user_url} 付款')
                    return
                elif message['3']['redReminder'] == '交易关闭':
                    user_id = message['1'].split('@')[0]
                    user_url = f'https://www.goofish.com/personal?userId={user_id}'
                    logger.info(f'买家 {user_url} 交易关闭')
                    return
                elif message['3']['redReminder'] == '等待卖家发货':
                    user_id = message['1'].split('@')[0]
                    user_url = f'https://www.goofish.com/personal?userId={user_id}'
                    logger.info(f'交易成功 {user_url} 等待卖家发货')
                    return

            except:
                pass

            # 判断消息类型
            if self.is_typing_status(message):
                logger.debug("用户正在输入")
                return
            elif not self.is_chat_message(message):
                logger.debug("其他非聊天消息")
                logger.debug(f"原始消息: {message}")
                return

            # 处理聊天消息
            create_time = int(message["1"]["5"])
            send_user_name = message["1"]["10"]["reminderTitle"]
            send_user_id = message["1"]["10"]["senderUserId"]
            send_message = message["1"]["10"]["reminderContent"]
            
            # 时效性验证（过滤5分钟前消息）
            if (time.time() * 1000 - create_time) > self.message_expire_time:
                logger.debug("过期消息丢弃")
                return
                
            # 获取商品ID和会话ID
            url_info = message["1"]["10"]["reminderUrl"]
            item_id = url_info.split("itemId=")[1].split("&")[0] if "itemId=" in url_info else None
            chat_id = message["1"]["2"].split('@')[0]
            
            if not item_id:
                logger.warning("无法获取商品ID")
                return

            # 检查是否为卖家（自己）发送的控制命令
            if send_user_id == self.myid:
                logger.debug("检测到卖家消息，检查是否为控制命令")
                
                # 检查切换命令
                if self.check_toggle_keywords(send_message):
                    mode = self.toggle_manual_mode(chat_id)
                    if mode == "manual":
                        logger.info(f"🔴 已接管会话 {chat_id} (商品: {item_id})")
                    else:
                        logger.info(f"🟢 已恢复会话 {chat_id} 的自动回复 (商品: {item_id})")
                    return
                
                # 记录卖家人工回复
                self.context_manager.add_message_by_chat(chat_id, self.myid, item_id, "assistant", send_message)
                logger.info(f"卖家人工回复 (会话: {chat_id}, 商品: {item_id}): {send_message}")
                return
            
            logger.info(f"用户: {send_user_name} (ID: {send_user_id}), 商品: {item_id}, 会话: {chat_id}, 消息: {send_message}")
            # 添加用户消息到上下文
            self.context_manager.add_message_by_chat(chat_id, send_user_id, item_id, "user", send_message)
            
            # 如果当前会话处于人工接管模式，不进行自动回复
            if self.is_manual_mode(chat_id):
                logger.info(f"🔴 会话 {chat_id} 处于人工接管模式，跳过自动回复")
                return
            if self.is_system_message(message):
                logger.debug("系统消息，跳过处理")
                return
            # 从数据库中获取商品信息，如果不存在则从API获取并保存
            item_info = self.context_manager.get_item_info(item_id)
            if not item_info:
                logger.info(f"从API获取商品信息: {item_id}")
                api_result = self.xianyu.get_item_info(item_id)
                if 'data' in api_result and 'itemDO' in api_result['data']:
                    item_info = api_result['data']['itemDO']
                    # 保存商品信息到数据库
                    self.context_manager.save_item_info(item_id, item_info)
                else:
                    logger.warning(f"获取商品信息失败: {api_result}")
                    return
            else:
                logger.info(f"从数据库获取商品信息: {item_id}")
                
            item_description = f"{item_info['desc']};当前商品售卖价格为:{str(item_info['soldPrice'])}"

            # 获取完整的对话上下文
            context = self.context_manager.get_context_by_chat(chat_id)

            # 生成回复 - 支持智能代理
            if hasattr(bot, 'update_product_from_api'):
                # 智能代理模式：更新商品信息到智能管理系统
                bot.update_product_from_api(item_id, item_info)
                bot_reply = bot.generate_reply(
                    send_message,
                    item_id,
                    item_description,
                    context=context
                )
            else:
                # 传统模式
                bot_reply = bot.generate_reply(
                    send_message,
                    item_description,
                    context=context
                )
            
            # 检查是否为价格意图，如果是则增加议价次数
            if bot.last_intent == "price":
                self.context_manager.increment_bargain_count_by_chat(chat_id)
                bargain_count = self.context_manager.get_bargain_count_by_chat(chat_id)
                logger.info(f"用户 {send_user_name} 对商品 {item_id} 的议价次数: {bargain_count}")
            
            # 添加机器人回复到上下文
            self.context_manager.add_message_by_chat(chat_id, self.myid, item_id, "assistant", bot_reply)
            
            logger.info(f"机器人回复: {bot_reply}")
            await self.send_msg(websocket, chat_id, send_user_id, bot_reply)
            
        except Exception as e:
            logger.error(f"处理消息时发生错误: {str(e)}")
            logger.debug(f"原始消息: {message_data}")

    async def send_heartbeat(self, ws):
        """发送心跳包并等待响应"""
        try:
            heartbeat_mid = generate_mid()
            heartbeat_msg = {
                "lwp": "/!",
                "headers": {
                    "mid": heartbeat_mid
                }
            }
            await ws.send(json.dumps(heartbeat_msg))
            self.last_heartbeat_time = time.time()
            logger.debug("心跳包已发送")
            return heartbeat_mid
        except Exception as e:
            logger.error(f"发送心跳包失败: {e}")
            raise

    async def heartbeat_loop(self, ws):
        """心跳维护循环"""
        while True:
            try:
                current_time = time.time()
                
                # 检查是否需要发送心跳
                if current_time - self.last_heartbeat_time >= self.heartbeat_interval:
                    await self.send_heartbeat(ws)
                
                # 检查上次心跳响应时间，如果超时则认为连接已断开
                if (current_time - self.last_heartbeat_response) > (self.heartbeat_interval + self.heartbeat_timeout):
                    logger.warning("心跳响应超时，可能连接已断开")
                    break
                
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"心跳循环出错: {e}")
                break

    async def handle_heartbeat_response(self, message_data):
        """处理心跳响应"""
        try:
            if (
                isinstance(message_data, dict)
                and "headers" in message_data
                and "mid" in message_data["headers"]
                and "code" in message_data
                and message_data["code"] == 200
            ):
                self.last_heartbeat_response = time.time()
                logger.debug("收到心跳响应")
                return True
        except Exception as e:
            logger.error(f"处理心跳响应出错: {e}")
        return False

    async def main(self):
        while True:
            try:
                # 重置连接重启标志
                self.connection_restart_flag = False
                
                headers = {
                    "Cookie": self.cookies_str,
                    "Host": "wss-goofish.dingtalk.com",
                    "Connection": "Upgrade",
                    "Pragma": "no-cache",
                    "Cache-Control": "no-cache",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                    "Origin": "https://www.goofish.com",
                    "Accept-Encoding": "gzip, deflate, br, zstd",
                    "Accept-Language": "zh-CN,zh;q=0.9",
                }

                async with websockets.connect(self.base_url, extra_headers=headers) as websocket:
                    self.ws = websocket
                    await self.init(websocket)
                    
                    # 初始化心跳时间
                    self.last_heartbeat_time = time.time()
                    self.last_heartbeat_response = time.time()
                    
                    # 启动心跳任务
                    self.heartbeat_task = asyncio.create_task(self.heartbeat_loop(websocket))
                    
                    # 启动token刷新任务
                    self.token_refresh_task = asyncio.create_task(self.token_refresh_loop())
                    
                    async for message in websocket:
                        try:
                            # 检查是否需要重启连接
                            if self.connection_restart_flag:
                                logger.info("检测到连接重启标志，准备重新建立连接...")
                                break
                                
                            message_data = json.loads(message)
                            
                            # 处理心跳响应
                            if await self.handle_heartbeat_response(message_data):
                                continue
                            
                            # 发送通用ACK响应
                            if "headers" in message_data and "mid" in message_data["headers"]:
                                ack = {
                                    "code": 200,
                                    "headers": {
                                        "mid": message_data["headers"]["mid"],
                                        "sid": message_data["headers"].get("sid", "")
                                    }
                                }
                                # 复制其他可能的header字段
                                for key in ["app-key", "ua", "dt"]:
                                    if key in message_data["headers"]:
                                        ack["headers"][key] = message_data["headers"][key]
                                await websocket.send(json.dumps(ack))
                            
                            # 处理其他消息
                            await self.handle_message(message_data, websocket)
                                
                        except json.JSONDecodeError:
                            logger.error("消息解析失败")
                        except Exception as e:
                            logger.error(f"处理消息时发生错误: {str(e)}")
                            logger.debug(f"原始消息: {message}")

            except websockets.exceptions.ConnectionClosed:
                logger.warning("WebSocket连接已关闭")
                
            except Exception as e:
                logger.error(f"连接发生错误: {e}")
                
            finally:
                # 清理任务
                if self.heartbeat_task:
                    self.heartbeat_task.cancel()
                    try:
                        await self.heartbeat_task
                    except asyncio.CancelledError:
                        pass
                        
                if self.token_refresh_task:
                    self.token_refresh_task.cancel()
                    try:
                        await self.token_refresh_task
                    except asyncio.CancelledError:
                        pass
                
                # 如果是主动重启，立即重连；否则等待5秒
                if self.connection_restart_flag:
                    logger.info("主动重启连接，立即重连...")
                else:
                    logger.info("等待5秒后重连...")
                    await asyncio.sleep(5)


def show_startup_dialog():
    """显示启动选择对话框"""
    import tkinter as tk
    from tkinter import messagebox, ttk

    # 创建启动选择窗口
    startup_root = tk.Tk()
    startup_root.title("XianyuAutoAgent - 启动选择")
    startup_root.geometry("500x350")
    startup_root.resizable(False, False)

    # 居中显示
    startup_root.update_idletasks()
    x = (startup_root.winfo_screenwidth() // 2) - (startup_root.winfo_width() // 2)
    y = (startup_root.winfo_screenheight() // 2) - (startup_root.winfo_height() // 2)
    startup_root.geometry(f"+{x}+{y}")

    # 设置图标和样式
    try:
        startup_root.iconbitmap("icon.ico")
    except:
        pass

    result = {"action": None}

    # 标题
    title_frame = ttk.Frame(startup_root)
    title_frame.pack(pady=20)

    title_label = ttk.Label(title_frame, text="🤖 XianyuAutoAgent", font=('Arial', 18, 'bold'))
    title_label.pack()

    subtitle_label = ttk.Label(title_frame, text="智能闲鱼客服助手", font=('Arial', 12))
    subtitle_label.pack(pady=(5, 0))

    # 分隔线
    separator = ttk.Separator(startup_root, orient='horizontal')
    separator.pack(fill='x', padx=20, pady=10)

    # 选项框架
    options_frame = ttk.Frame(startup_root)
    options_frame.pack(expand=True, fill='both', padx=40, pady=20)

    # 配置选项
    config_frame = ttk.LabelFrame(options_frame, text="🛠️ 配置管理", padding=15)
    config_frame.pack(fill='x', pady=(0, 15))

    config_desc = ttk.Label(config_frame, text="打开配置界面，设置Cookie、AI模型、商品策略等",
                           font=('Arial', 10), foreground='gray')
    config_desc.pack(anchor='w')

    def open_config():
        result["action"] = "config"
        startup_root.quit()

    config_btn = ttk.Button(config_frame, text="🔧 打开配置界面", command=open_config, width=20)
    config_btn.pack(pady=(10, 0))

    # 直接启动选项
    start_frame = ttk.LabelFrame(options_frame, text="🚀 直接启动", padding=15)
    start_frame.pack(fill='x', pady=(0, 15))

    start_desc = ttk.Label(start_frame, text="使用当前配置直接启动智能客服服务",
                          font=('Arial', 10), foreground='gray')
    start_desc.pack(anchor='w')

    def direct_start():
        result["action"] = "start"
        startup_root.quit()

    start_btn = ttk.Button(start_frame, text="▶️ 直接启动服务", command=direct_start, width=20)
    start_btn.pack(pady=(10, 0))

    # 退出选项
    exit_frame = ttk.Frame(options_frame)
    exit_frame.pack(fill='x')

    def exit_app():
        result["action"] = "exit"
        startup_root.quit()

    exit_btn = ttk.Button(exit_frame, text="❌ 退出", command=exit_app, width=20)
    exit_btn.pack()

    # 状态信息
    status_frame = ttk.Frame(startup_root)
    status_frame.pack(side='bottom', fill='x', padx=20, pady=10)

    # 检查配置状态
    try:
        load_dotenv()
        cookies_str = os.getenv("COOKIES_STR")
        if cookies_str:
            status_text = "✅ 配置文件已就绪"
            status_color = "green"
        else:
            status_text = "⚠️ 需要配置Cookie信息"
            status_color = "orange"
    except:
        status_text = "❌ 配置文件异常"
        status_color = "red"

    status_label = ttk.Label(status_frame, text=status_text, foreground=status_color)
    status_label.pack()

    # 运行对话框
    startup_root.mainloop()
    startup_root.destroy()

    return result["action"]

def run_config_gui():
    """运行配置界面"""
    try:
        from config_gui import ConfigGUI
        logger.info("🔧 启动配置界面...")
        app = ConfigGUI()
        app.run()
        return True
    except Exception as e:
        logger.error(f"配置界面启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_service():
    """运行主服务"""
    global bot
    try:
        # 加载环境变量
        load_dotenv()

        # 配置日志级别
        log_level = os.getenv("LOG_LEVEL", "DEBUG").upper()
        logger.remove()  # 移除默认handler
        logger.add(
            sys.stderr,
            level=log_level,
            format="<green>{time:YYYY-MM-DD HH:mm:ss.SSS}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
        )
        logger.info(f"日志级别设置为: {log_level}")

        cookies_str = os.getenv("COOKIES_STR")
        if not cookies_str:
            logger.error("❌ 未找到COOKIES_STR环境变量，请先配置Cookie信息")
            return False

        # 初始化智能代理（包含传统机器人作为备用）
        try:
            bot = SmartXianyuAgent()
            logger.info("✅ 使用智能代理模式")
        except Exception as e:
            logger.warning(f"智能代理初始化失败，回退到传统模式: {e}")
            bot = XianyuReplyBot()

        xianyuLive = XianyuLive(cookies_str)
        logger.info("🚀 启动XianyuAutoAgent服务...")

        # 常驻进程
        asyncio.run(xianyuLive.main())

    except KeyboardInterrupt:
        logger.info("👋 用户中断，服务已停止")
        return True
    except Exception as e:
        logger.error(f"❌ 服务运行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    logger.info("🚀 XianyuAutoAgent 启动中...")

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--config":
            # 直接启动配置界面
            run_config_gui()
            sys.exit(0)
        elif sys.argv[1] == "--service":
            # 直接启动服务
            success = run_service()
            sys.exit(0 if success else 1)
        elif sys.argv[1] == "--help":
            print("XianyuAutoAgent - 智能闲鱼客服助手")
            print("用法:")
            print("  python main.py           # 显示启动选择界面")
            print("  python main.py --config  # 直接打开配置界面")
            print("  python main.py --service # 直接启动服务")
            print("  python main.py --help    # 显示帮助信息")
            sys.exit(0)

    # 显示启动选择对话框
    action = show_startup_dialog()

    if action == "config":
        # 打开配置界面
        if run_config_gui():
            logger.info("✅ 配置界面已关闭")
        else:
            logger.error("❌ 配置界面运行失败")
    elif action == "start":
        # 直接启动服务
        success = run_service()
        if not success:
            sys.exit(1)
    elif action == "exit":
        logger.info("👋 用户选择退出")
        sys.exit(0)
    else:
        logger.info("👋 用户关闭启动对话框")
        sys.exit(0)
