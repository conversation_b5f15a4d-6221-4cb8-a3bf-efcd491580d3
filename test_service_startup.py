#!/usr/bin/env python3
"""
测试服务启动 - 模拟配置界面的启动过程
"""

import os
import sys
import subprocess
import time
from dotenv import load_dotenv

def test_service_startup():
    """测试服务启动过程"""
    print("🔧 测试服务启动过程...")
    
    try:
        # 加载环境变量
        load_dotenv()
        
        # 检查配置文件
        if not os.path.exists('.env'):
            print("❌ .env文件不存在，请先配置")
            return False
            
        # 检查必要的配置
        cookies_str = os.getenv('COOKIES_STR', '')
        api_key = os.getenv('API_KEY', '')
        
        if not cookies_str:
            print("❌ COOKIES_STR未配置")
            return False
            
        if not api_key:
            print("❌ API_KEY未配置")
            return False
            
        print("✅ 基础配置检查通过")
        
        # 模拟配置界面的启动方式
        print("\n🚀 启动服务进程...")
        print("注意：这将在新窗口中启动服务，请查看新窗口的输出")
        print("按Ctrl+C停止测试")
        
        # 启动服务进程（与config_gui.py中相同的方式）
        process = subprocess.Popen([
            sys.executable, "main.py", "--service"
        ], cwd=os.getcwd(), creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0)
        
        print(f"✅ 服务进程已启动，PID: {process.pid}")
        print("📝 请查看新打开的控制台窗口中的日志输出")
        print("📁 同时检查 logs/xianyu_agent.log 文件")
        
        # 等待一段时间让服务启动
        print("\n⏳ 等待服务启动...")
        time.sleep(5)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            print("✅ 服务进程正在运行")
            
            # 检查日志文件
            log_file = "logs/xianyu_agent.log"
            if os.path.exists(log_file):
                print(f"📝 检查日志文件: {log_file}")
                try:
                    with open(log_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        if lines:
                            print("📄 最新日志内容:")
                            for line in lines[-10:]:  # 显示最后10行
                                print(f"  {line.strip()}")
                        else:
                            print("⚠️ 日志文件为空")
                except Exception as e:
                    print(f"❌ 读取日志文件失败: {e}")
            else:
                print("⚠️ 日志文件不存在")
                
        else:
            print("❌ 服务进程已退出")
            return False
            
        # 询问是否终止进程
        input("\n按回车键终止服务进程...")
        
        try:
            process.terminate()
            process.wait(timeout=5)
            print("✅ 服务进程已终止")
        except subprocess.TimeoutExpired:
            process.kill()
            print("⚠️ 强制终止服务进程")
            
        return True
        
    except KeyboardInterrupt:
        print("\n👋 用户中断测试")
        if 'process' in locals() and process.poll() is None:
            process.terminate()
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_logs():
    """检查现有日志"""
    print("📝 检查现有日志文件...")
    
    log_file = "logs/xianyu_agent.log"
    if os.path.exists(log_file):
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                if lines:
                    print(f"📄 日志文件包含 {len(lines)} 行")
                    print("📄 最新10行日志:")
                    for line in lines[-10:]:
                        print(f"  {line.strip()}")
                else:
                    print("⚠️ 日志文件为空")
        except Exception as e:
            print(f"❌ 读取日志文件失败: {e}")
    else:
        print("⚠️ 日志文件不存在")

if __name__ == "__main__":
    print("选择操作:")
    print("1. 检查现有日志")
    print("2. 测试服务启动")
    
    choice = input("请输入选择 (1/2): ").strip()
    
    if choice == "1":
        check_logs()
    elif choice == "2":
        success = test_service_startup()
        print(f"\n{'='*50}")
        print(f"测试结果: {'✅ 成功' if success else '❌ 失败'}")
    else:
        print("无效选择")
    
    input("按回车键退出...")
