#!/usr/bin/env python3
"""
启动Web配置管理界面
"""

import os
import sys
import webbrowser
import time
import subprocess
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    try:
        import flask
        print("✅ Flask已安装")
        return True
    except ImportError:
        print("❌ Flask未安装")
        print("正在安装Flask...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "flask==3.0.0"])
            print("✅ Flask安装成功")
            return True
        except subprocess.CalledProcessError:
            print("❌ Flask安装失败，请手动安装: pip install flask==3.0.0")
            return False

def ensure_directories():
    """确保必要的目录存在"""
    directories = ["templates", "data", "logs", "prompts"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ 目录已创建: {directory}")

def main():
    """主函数"""
    print("🌐 XianyuAutoAgent Web配置管理界面")
    print("=" * 50)
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 确保目录存在
    ensure_directories()
    
    # 检查模板文件是否存在
    template_file = Path("templates/config.html")
    if not template_file.exists():
        print("❌ 模板文件不存在，请确保templates/config.html文件存在")
        return 1
    
    print("\n🚀 启动Web服务器...")
    print("📱 配置界面将在浏览器中自动打开")
    print("🔧 您可以在界面中配置所有设置")
    print("💾 配置会自动保存到.env文件和prompts目录")
    print("\n" + "=" * 50)
    
    # 延迟打开浏览器
    def open_browser():
        time.sleep(2)  # 等待服务器启动
        webbrowser.open('http://localhost:5000')
    
    import threading
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动Flask应用
    try:
        from web_config import app
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        print("\n\n👋 Web配置界面已关闭")
        print("🎉 感谢使用XianyuAutoAgent配置管理！")
        return 0
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
