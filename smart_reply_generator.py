#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能回复生成器
支持通用模式和针对性模式的智能回复生成
"""

import json
import re
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from loguru import logger
from openai import OpenAI

from smart_product_manager import SmartProductManager, ProductInfo, ReplyStrategy
from config import config


class SmartReplyGenerator:
    """智能回复生成器"""

    def __init__(self, product_manager: SmartProductManager):
        self.product_manager = product_manager
        self.config = config
        
        # 初始化AI客户端
        self.client = OpenAI(
            api_key=self.config.ai.api_key,
            base_url=self.config.ai.base_url
        )
        
        # 通用模式提示词
        self.general_system_prompt = """你是一个专业的闲鱼客服助手，需要根据商品信息和用户消息生成合适的回复。

回复要求：
1. 语气友好、自然，像真人对话
2. 根据商品信息准确回答问题
3. 适当推销商品优点，但不要过于夸张
4. 价格相关问题要合理议价，不能无底线降价
5. 回复简洁明了，不要太长

请根据以下信息生成回复："""

        # 针对性模式提示词
        self.specific_system_prompt = """你是一个专业的闲鱼客服助手，需要根据预设的回复策略和模板生成个性化回复。

回复要求：
1. 严格按照预设的回复策略执行
2. 使用提供的回复模板作为参考
3. 保持一致的回复风格和语调
4. 遵守价格底线和议价规则
5. 突出商品的特殊卖点

请根据以下信息生成回复："""

    def generate_reply(self, 
                      user_message: str, 
                      item_id: str, 
                      context: List[Dict] = None,
                      intent: str = "general") -> str:
        """
        生成智能回复
        
        Args:
            user_message: 用户消息
            item_id: 商品ID
            context: 对话上下文
            intent: 意图类型 (general, price, tech等)
            
        Returns:
            str: 生成的回复
        """
        try:
            # 获取商品信息
            product = self.product_manager.get_product(item_id)
            if not product:
                return "抱歉，暂时无法获取商品信息，请稍后再试。"
            
            # 获取回复策略
            strategy = self.product_manager.get_reply_strategy(item_id)
            
            # 根据模式选择生成方法
            if strategy.mode == "specific":
                return self._generate_specific_reply(user_message, product, strategy, context, intent)
            else:
                return self._generate_general_reply(user_message, product, strategy, context, intent)
                
        except Exception as e:
            logger.error(f"生成回复失败: {e}")
            return "抱歉，我现在有点忙，请稍后再联系我。"
    
    def _generate_general_reply(self, 
                               user_message: str, 
                               product: ProductInfo, 
                               strategy: ReplyStrategy,
                               context: List[Dict],
                               intent: str) -> str:
        """生成通用模式回复"""
        
        # 自动分析商品信息
        analysis = self.product_manager.analyze_product_automatically(product)
        
        # 构建提示词
        prompt = self._build_general_prompt(user_message, product, analysis, context, intent)
        
        # 调用AI生成回复
        messages = [
            {"role": "system", "content": self.general_system_prompt},
            {"role": "user", "content": prompt}
        ]
        
        return self._call_ai(messages)
    
    def _generate_specific_reply(self, 
                                user_message: str, 
                                product: ProductInfo, 
                                strategy: ReplyStrategy,
                                context: List[Dict],
                                intent: str) -> str:
        """生成针对性模式回复"""
        
        # 检查是否有预设模板
        template = self._get_template_by_intent(strategy, intent)
        
        if template:
            # 使用模板生成回复
            return self._generate_from_template(template, user_message, product, strategy, context)
        else:
            # 使用AI生成但遵循策略
            prompt = self._build_specific_prompt(user_message, product, strategy, context, intent)
            
            messages = [
                {"role": "system", "content": self.specific_system_prompt},
                {"role": "user", "content": prompt}
            ]
            
            return self._call_ai(messages)
    
    def _build_general_prompt(self, 
                             user_message: str, 
                             product: ProductInfo, 
                             analysis: Dict,
                             context: List[Dict],
                             intent: str) -> str:
        """构建通用模式提示词"""
        
        prompt_parts = [
            f"商品信息：",
            f"- 标题：{product.title}",
            f"- 价格：{product.price}元",
            f"- 描述：{product.description[:200]}...",
            f"- 状态：{product.stock_status}",
            ""
        ]
        
        # 添加分析结果
        if analysis.get("selling_points"):
            prompt_parts.append(f"商品卖点：{', '.join(analysis['selling_points'])}")
        
        if analysis.get("keywords"):
            prompt_parts.append(f"关键词：{', '.join(analysis['keywords'])}")
        
        # 添加价格分析
        price_analysis = analysis.get("price_analysis", {})
        if price_analysis:
            prompt_parts.append(f"价格分析：{price_analysis}")
        
        prompt_parts.extend([
            "",
            f"用户消息：{user_message}",
            f"意图类型：{intent}",
            ""
        ])
        
        # 添加上下文
        if context:
            prompt_parts.append("对话历史：")
            for msg in context[-3:]:  # 只取最近3条
                role = "用户" if msg["role"] == "user" else "客服"
                prompt_parts.append(f"{role}：{msg['content']}")
            prompt_parts.append("")
        
        prompt_parts.append("请生成一个合适的回复：")
        
        return "\n".join(prompt_parts)
    
    def _build_specific_prompt(self, 
                              user_message: str, 
                              product: ProductInfo, 
                              strategy: ReplyStrategy,
                              context: List[Dict],
                              intent: str) -> str:
        """构建针对性模式提示词"""
        
        prompt_parts = [
            f"商品信息：",
            f"- 标题：{product.title}",
            f"- 价格：{product.price}元",
            f"- 描述：{product.description[:200]}...",
            "",
            f"回复策略：",
            f"- 最低价格：{strategy.min_price}元",
            f"- 最大折扣：{strategy.max_discount_percent}%",
            f"- 议价轮数：{strategy.bargain_rounds}",
            ""
        ]
        
        # 添加特殊说明
        if strategy.special_notes:
            prompt_parts.append(f"特殊说明：{strategy.special_notes}")
        
        # 添加卖点
        if strategy.selling_points:
            prompt_parts.append(f"重点卖点：{', '.join(strategy.selling_points)}")
        
        prompt_parts.extend([
            "",
            f"用户消息：{user_message}",
            f"意图类型：{intent}",
            ""
        ])
        
        # 添加上下文
        if context:
            prompt_parts.append("对话历史：")
            for msg in context[-3:]:
                role = "用户" if msg["role"] == "user" else "客服"
                prompt_parts.append(f"{role}：{msg['content']}")
            prompt_parts.append("")
        
        prompt_parts.append("请根据策略生成回复：")
        
        return "\n".join(prompt_parts)
    
    def _get_template_by_intent(self, strategy: ReplyStrategy, intent: str) -> Optional[str]:
        """根据意图获取对应模板"""
        template_map = {
            "general": strategy.greeting_template,
            "price": strategy.price_response_template,
            "tech": strategy.tech_response_template,
            "final": strategy.final_price_template
        }
        
        template = template_map.get(intent, "")
        return template if template.strip() else None
    
    def _generate_from_template(self, 
                               template: str, 
                               user_message: str, 
                               product: ProductInfo, 
                               strategy: ReplyStrategy,
                               context: List[Dict]) -> str:
        """从模板生成回复"""
        
        # 模板变量替换
        variables = {
            "{product_title}": product.title,
            "{product_price}": str(product.price),
            "{min_price}": str(strategy.min_price),
            "{user_message}": user_message,
            "{special_notes}": strategy.special_notes
        }
        
        reply = template
        for var, value in variables.items():
            reply = reply.replace(var, value)
        
        return reply
    
    def _call_ai(self, messages: List[Dict]) -> str:
        """调用AI生成回复"""
        try:
            response = self.client.chat.completions.create(
                model=self.config.ai.model_name,
                messages=messages,
                temperature=self.config.ai.temperature,
                max_tokens=self.config.ai.max_tokens,
                top_p=self.config.ai.top_p
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"AI调用失败: {e}")
            return "抱歉，我现在有点忙，请稍后再联系我。"
    
    def calculate_bargain_price(self, 
                               product: ProductInfo, 
                               strategy: ReplyStrategy, 
                               bargain_round: int) -> float:
        """计算议价价格"""
        
        # 基础价格
        base_price = product.price
        
        # 最低价格
        min_price = max(strategy.min_price, base_price * (1 - strategy.max_discount_percent / 100))
        
        # 根据议价轮数计算折扣
        if bargain_round >= strategy.bargain_rounds:
            # 达到最大轮数，给出最低价
            return min_price
        else:
            # 渐进式降价
            discount_step = strategy.max_discount_percent / strategy.bargain_rounds
            current_discount = discount_step * bargain_round
            suggested_price = base_price * (1 - current_discount / 100)
            
            return max(suggested_price, min_price)
    
    def is_price_acceptable(self, 
                           product: ProductInfo, 
                           strategy: ReplyStrategy, 
                           offered_price: float) -> bool:
        """判断用户出价是否可接受"""
        min_price = max(strategy.min_price, product.price * (1 - strategy.max_discount_percent / 100))
        return offered_price >= min_price
